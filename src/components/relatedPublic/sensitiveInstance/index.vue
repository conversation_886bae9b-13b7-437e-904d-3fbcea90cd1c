<template>
  <div class="container">
    <el-button @click="handleBackHome" class="button-backhome">
      <div class="inner">
        <img src="../../../assets/images/backhome.png" alt="">
        <span class="button-inner">返回主屏</span>
      </div>
    </el-button>
    <div class="container-left">
      <myAttention :jjdbh="jjdbh" :positive-instance-data="pointInstanceData" :district-list="districtList" @row-click="handleTableClick" />
      <!-- <warningInstanceRate/> -->
    </div>
    <div class="container-center">
      <warningInstanceDetail v-if="zxJqDetailData" :zx-jq-detail-data="zxJqDetailData" />
      <div v-else class="no-data">暂无数据</div>
      <warningInstanceProcess v-if="zxJqProcessData" :zx-jq-process-data="zxJqProcessData" @node-click="handleClickIndex" />
      <div v-else class="no-data">暂无数据</div>
      <emergencyResponse v-if="zxJqProcessList" :zx-jq-process-list="zxJqProcessList" />
      <div v-else class="no-data">暂无数据</div>
    </div>
    <div class="container-right">
      <div class="container-right-1">
        <div class="container-title">
          <span>风险标签</span>
        </div>
        <div style="display: flex; gap:10px;font-size: 16px;">
          <div
              v-if="zxJqDetailData && zxJqDetailData.fxdj && zxJqDetailData.fxdj !== '' && zxJqDetailData.fxdj !== null"
              class="tag-group-1"
          >
            <el-tag
                :type="zxJqDetailData.fxdj === 3 ? 'danger' : zxJqDetailData.fxdj === 2 ? 'warning' : zxJqDetailData.fxdj === 1 ? 'success' : ''"
                effect="dark"
            >库匹配</el-tag>
          </div>
          <div
              v-if="zxJqDetailData && zxJqDetailData.zntfxdj && zxJqDetailData.zntfxdj !== ''"
              class="tag-group-1"
          >
            <el-tag
                :type="zxJqDetailData.zntfxdj === '高风险' ? 'danger' : zxJqDetailData.zntfxdj === '中风险' ? 'warning' : zxJqDetailData.zntfxdj === '低风险' ? 'success' : ''"
                effect="dark"
            >{{
                `模型分析：${
                    zxJqDetailData
                        .zntfxdj === '高风险' ? '高风险' : zxJqDetailData.zntfxdj === '中风险' ? '中风险' : zxJqDetailData.zntfxdj === '低风险' ? '低风险' : ''
                }` }}</el-tag>
          </div>
        </div>
        <div class="sec">
          <div class="ul">
            <div v-if="zxJqDetailData && zxJqDetailData.sfmg === 1" class="time">
              <div style="margin-bottom: 10px;color: #fff;font-size: 20px;">
                库匹配结果：
              </div>
              <div class="d" style="display:flex; flex-wrap: wrap; gap: 8px;">
                <!-- MCN机构标签 -->
                <el-tag
                    v-for="(tag, index) in tagData.tagMcn"
                    :key="'mcn-' + tag.id + '-' + index"
                    type="primary"
                    style="cursor: pointer; margin-right: 5px; margin-bottom: 5px;"
                    @click="handleTagClick('mcn', tag.id, tag.label)"
                >
                  MCN: {{ tag.label }}
                </el-tag>
                <!-- 重点大V账号标签 -->
                <el-tag
                    v-for="(tag, index) in tagData.tagBigV"
                    :key="'bigv-' + tag.id + '-' + index"
                    type="success"
                    style="cursor: pointer; margin-right: 5px; margin-bottom: 5px;"
                    @click="handleTagClick('bigv', tag.id, tag.label)"
                >
                  大V: {{ tag.label }}
                </el-tag>
                <!-- 涉舆重点人员库标签 -->
                <el-tag
                    v-for="(tag, index) in tagData.tagPersons"
                    :key="'persons-' + tag.id + '-' + index"
                    type="warning"
                    style="cursor: pointer; margin-right: 5px; margin-bottom: 5px;"
                    @click="handleTagClick('persons', tag.id, tag.label)"
                >
                  人员: {{ tag.label }}
                </el-tag>
              </div>
            </div>
            <div v-if="zxJqDetailData && zxJqDetailData.zntfxdj && zxJqDetailData.zntfxdj !== ''" class="time" style="margin-top: 10px;">
              <div style="margin-bottom: 10px;color: #fff;font-size: 20px;">
                模型分析原因：
              </div>
              <div class="d" style="line-height: 1.5; color: #fff;height: 195px; overflow-y: auto;">
                {{ zxJqDetailData.zntfxyy }}
              </div>
            </div>
          </div>
        </div>
<!--        <performanceDisplay v-if="zxJqResourceData1" :zx-jq-resource-data="zxJqResourceData1" />-->
<!--        <div v-else class="no-data">暂无数据</div>-->
      </div>
      <div class="container-right-2">
        <div class="container-title">
          <span>执法记录仪</span>
        </div>
        <!--        <performanceDisplay v-if="zxJqResourceData2" :zx-jq-resource-data="zxJqResourceData2" />-->
        <!--        <div v-else class="no-data">暂无数据</div>-->
        <div v-show="isVideoShow" class="video-tab-more">
          <div class="close" @click="closeVideoTabMore">
            <i class="el-icon-close" />
          </div>
          <div class="video-tab-more-sec">
            <video class="video-more" poster="" :src="videoSrcMore" controls autoplay />
          </div>
        </div>
        <div class="video">
          <div class="video-tab-main">
            <div class="ul">
              <div v-for="(item,idx) in videoTabMainList" :key="idx" class="li" :class="videoTabMainIdx==idx?'active':''">
                <!--                  @click="videoTabMainClick(idx)"-->
                {{ item.name }}
              </div>
            </div>
          </div>
          <div class="video-tab">
            <div v-if="videoTabMainIdx==1" class="ul">
              <div v-for="(item,idx) in videoTabList" :key="idx" class="li" :class="videoTabIdx==idx?'active':''" @click="videoTabClick(idx)">
                {{ item.name }}{{ idx+1 }}
              </div>
            </div>
            <div v-if="videoTabMainIdx==0" class="tab-select">
              <el-select v-model="selectValue" placeholder="请选择" @change="selectChange">
                <el-option
                    v-for="item in selectList"
                    :key="item.value"
                    :label="item.name"
                    :value="item.value"
                />
              </el-select>
            </div>
          </div>
          <div v-if="videoTabMainIdx==0" class="video-sec-1">
            <video v-if="videoSrc" ref="jkvideo" class="video" poster="" :src="videoSrc" controls />
            <div v-else class="none">
              暂无视频
            </div>
            <div v-if="videoSrc" class="video-sec-fd">
              <i class="el-icon-full-screen" @click="openVideoTabMore" />
            </div>
          </div>
          <div v-if="videoTabMainIdx==1" class="video-sec">
            <div id="playWnd" class="playWnd" style="left: 0px;top: 0px" />
            <div class="video-sec-more">
              暂无单兵视频
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- 新增：标签详情悬浮窗 -->
    <el-dialog
        :title="tagDialogTitle"
        :visible.sync="tagDialogVisible"
        width="80%"
        :before-close="() => { tagDialogVisible = false }"
        custom-class="custom-dialog"
    >
      <div v-if="tagDialogContent">
        <!-- MCN机构详情 -->
        <div v-if="currentTagType === 'mcn'" class="tag-detail-content">
          <el-descriptions :column="2" border class="custom-descriptions">
            <el-descriptions-item label="机构名称">{{ tagDialogContent.jgmc || '暂无' }}</el-descriptions-item>
            <el-descriptions-item label="公司法人">{{ tagDialogContent.gsfr || '暂无' }}</el-descriptions-item>
            <el-descriptions-item label="法人联系方式">{{ tagDialogContent.frlxfs || '暂无' }}</el-descriptions-item>
            <el-descriptions-item label="法人身份证号码">{{ tagDialogContent.frsfzhm || '暂无' }}</el-descriptions-item>
            <el-descriptions-item label="机构所在地">{{ tagDialogContent.jgszd || '暂无' }}</el-descriptions-item>
            <el-descriptions-item label="具体地址">{{ tagDialogContent.jtdz || '暂无' }}</el-descriptions-item>
            <el-descriptions-item label="运营主体">{{ tagDialogContent.yyzt || '暂无' }}</el-descriptions-item>
            <el-descriptions-item label="粉丝数">{{ tagDialogContent.fss || '暂无' }}</el-descriptions-item>
            <el-descriptions-item label="签约主播数">{{ tagDialogContent.qyzbs || '暂无' }}</el-descriptions-item>
            <el-descriptions-item label="签约头部主播数">{{ tagDialogContent.qytbzbs || '暂无' }}</el-descriptions-item>
            <el-descriptions-item label="头部主播账号">{{ tagDialogContent.tbzbzh || '暂无' }}</el-descriptions-item>
            <el-descriptions-item label="主播身份证号码">{{ tagDialogContent.zbsfzhm || '暂无' }}</el-descriptions-item>
          </el-descriptions>
        </div>

        <!-- 涉舆重点人员库详情 -->
        <div v-if="currentTagType === 'persons'" class="tag-detail-content">
          <el-descriptions :column="2" border class="custom-descriptions">
            <el-descriptions-item label="账号昵称">{{ tagDialogContent.zhnc || '暂无' }}</el-descriptions-item>
            <el-descriptions-item label="账号运营者姓名">{{ tagDialogContent.zhyyzxm || '暂无' }}</el-descriptions-item>
            <el-descriptions-item label="身份证号码">{{ tagDialogContent.sfzhm || '暂无' }}</el-descriptions-item>
            <el-descriptions-item label="平台">{{ tagDialogContent.pt || '暂无' }}</el-descriptions-item>
            <el-descriptions-item label="平台ID">{{ tagDialogContent.ptid || '暂无' }}</el-descriptions-item>
            <el-descriptions-item label="注册号码">{{ tagDialogContent.zchm || '暂无' }}</el-descriptions-item>
            <el-descriptions-item label="账号属性">{{ tagDialogContent.zhsx || '暂无' }}</el-descriptions-item>
            <el-descriptions-item label="账号内容类型">{{ tagDialogContent.zhnrlx || '暂无' }}</el-descriptions-item>
            <el-descriptions-item label="粉丝数量">{{ tagDialogContent.fshl || '暂无' }}</el-descriptions-item>
            <el-descriptions-item label="工作单位">{{ tagDialogContent.gzdw || '暂无' }}</el-descriptions-item>
            <el-descriptions-item label="居住地址">{{ tagDialogContent.jzdz || '暂无' }}</el-descriptions-item>
            <el-descriptions-item label="账号主页链接">{{ tagDialogContent.zhzylj || '暂无' }}</el-descriptions-item>
            <el-descriptions-item label="账号违规记录">{{ tagDialogContent.zhwgjl || '暂无' }}</el-descriptions-item>
            <el-descriptions-item label="运营者违法犯罪前科">{{ tagDialogContent.yyzwffzqk || '暂无' }}</el-descriptions-item>
            <el-descriptions-item label="涉及事件">{{ tagDialogContent.sjsj || '暂无' }}</el-descriptions-item>
            <el-descriptions-item label="事件发生时间">{{ tagDialogContent.sjfssj || '暂无' }}</el-descriptions-item>
            <el-descriptions-item label="参与次数">{{ tagDialogContent.cycs || '暂无' }}</el-descriptions-item>
            <el-descriptions-item label="诉求">{{ tagDialogContent.sq || '暂无' }}</el-descriptions-item>
            <el-descriptions-item label="是否闭环">{{ tagDialogContent.sfbh || '暂无' }}</el-descriptions-item>
            <el-descriptions-item label="备注">{{ tagDialogContent.bz || '暂无' }}</el-descriptions-item>
          </el-descriptions>
        </div>

        <!-- 重点大V账号详情 -->
        <div v-if="currentTagType === 'bigv'" class="tag-detail-content">
          <el-descriptions :column="2" border class="custom-descriptions">
            <el-descriptions-item label="账号昵称">{{ tagDialogContent.zhnc || '暂无' }}</el-descriptions-item>
            <el-descriptions-item label="账号运营者姓名">{{ tagDialogContent.zhyyz || '暂无' }}</el-descriptions-item>
            <el-descriptions-item label="身份证号码">{{ tagDialogContent.sfzhm || '暂无' }}</el-descriptions-item>
            <el-descriptions-item label="平台">{{ tagDialogContent.pt || '暂无' }}</el-descriptions-item>
            <el-descriptions-item label="用户ID">{{ tagDialogContent.yhid || '暂无' }}</el-descriptions-item>
            <el-descriptions-item label="注册号码">{{ tagDialogContent.zchm || '暂无' }}</el-descriptions-item>
            <el-descriptions-item label="账号属性">{{ tagDialogContent.zhsx || '暂无' }}</el-descriptions-item>
            <el-descriptions-item label="账号内容类型">{{ tagDialogContent.zhnrlx || '暂无' }}</el-descriptions-item>
            <el-descriptions-item label="粉丝量">{{ tagDialogContent.fsl || '暂无' }}</el-descriptions-item>
            <el-descriptions-item label="工作单位">{{ tagDialogContent.gzdw || '暂无' }}</el-descriptions-item>
            <el-descriptions-item label="所属MCN机构（工作单位）">{{ tagDialogContent.ssjg || '暂无' }}</el-descriptions-item>
            <el-descriptions-item label="机构(工作单位)法人">{{ tagDialogContent.jgfr || '暂无' }}</el-descriptions-item>
            <el-descriptions-item label="法人联系方式">{{ tagDialogContent.frlxfs || '暂无' }}</el-descriptions-item>
            <el-descriptions-item label="运营主体">{{ tagDialogContent.yyzt || '暂无' }}</el-descriptions-item>
            <el-descriptions-item label="签约主播数量">{{ tagDialogContent.zbqysl || '暂无' }}</el-descriptions-item>
            <el-descriptions-item label="居住地址">{{ tagDialogContent.jzdz || '暂无' }}</el-descriptions-item>
            <el-descriptions-item label="具体地址">{{ tagDialogContent.jtdz || '暂无' }}</el-descriptions-item>
            <el-descriptions-item label="主页链接">{{ tagDialogContent.zylj || '暂无' }}</el-descriptions-item>
            <el-descriptions-item label="账号违规记录">{{ tagDialogContent.zhwgjl || '暂无' }}</el-descriptions-item>
            <el-descriptions-item label="运营者违法犯罪前科">{{ tagDialogContent.yyzwffzqk || '暂无' }}</el-descriptions-item>
          </el-descriptions>
        </div>
      </div>
      <div v-else>
        <p>暂无详细信息</p>
      </div>
<!--      <span slot="footer" class="dialog-footer">-->
<!--        <el-button @click="tagDialogVisible = false">关闭</el-button>-->
<!--      </span>-->
    </el-dialog>

  </div>
</template>

<script>
// 左侧组件
import myAttention from '@/components/positiveRublicize/warningInstance/myAttention.vue'
import warningInstanceRate from '@/components/positiveRublicize/warningInstance/warningInstanceRate.vue'
// 中间组件
import warningInstanceDetail from '@/components/positiveRublicize/warningInstance/warningInstanceDetail.vue'
import warningInstanceProcess from '@/components/positiveRublicize/warningInstance/warningInstanceProcess.vue'
// 右侧组件
import emergencyResponse from '@/components/positiveRublicize/warningInstance/emergencyResponse.vue'
import performanceDisplay from '@/components/positiveRublicize/warningInstance/performanceDisplay.vue'
import { getJqDetail, getJqProcess, getList, tagBigVDetails, tagMcnDetails, tagPersonsDetails } from '@/api/screen'
import axios from 'axios'

var initCount = 0
var pubKey = ''
var oWebControl
export default {
  name: 'SensitiveInstance',
  components: {
    myAttention, // 我的关注
    warningInstanceRate, // 警情类型占比
    warningInstanceDetail, // 警情详情
    warningInstanceProcess, // 流程
    emergencyResponse, // 出警处置
    performanceDisplay // 成果展示
  },
  props: {
    pointInstanceData: {
      type: Array,
      required: true
    },
    districtList: {
      type: Array,
      required: true
    },
    jjdbh: {
      type: String,
      required: true
    }
  },
  data() {
    return {
      zxJqDetailData: null,
      zxJqResourceData1: null,
      zxJqResourceData2: null,
      zxJqProcessData: null,
      zxJqProcessList: null,
      jqTypeProportionData: null,
      id: null,
      isMounted: false,
      videoSrc: '',
      videoSrcMore: '',
      videoImg: '',
      videoTabMainIdx: 0,
      videoTabMainList: [{
        name: '视频监控',
        value: 'spjk'
      },
        // {
        //   name: '4G单兵',
        //   value: '4gdb'
        // }
      ],
      videoTabIdx: 0,
      videoTabList: [],
      selectValue: '',
      selectList: [],
      isVideoShow: false,
      jygh: null,
      tagData: {
        tagMcn: [],
        tagBigV: [],
        tagPersons: []
      },
      // 悬浮窗控制
      tagDialogVisible: false,
      tagDialogTitle: '',
      tagDialogContent: '',
      currentTagType: '',

    }
  },
  created() {
    this.handleTableClick()
    this.isMounted = true
  },
  mounted() {
    // this.getJqDetails()
  },
  watch: {
    positiveInstanceData: {
      handler(val) {
        // console.log(val)
      },
      immediate: true,
      deep: true
    },
    jjdbh: {
      handler(val) {
        // console.log(val)
      },
      immediate: true
    },
    districtList: {
      handler(val) {
        // console
      }
    },
  },
  methods: {
    // 将URL转换为HTTPS并去掉端口号
    convertToHttpsWithoutPort(url) {
      if (!url) return url

      try {
        // 创建URL对象来解析URL
        const urlObj = new URL(url)

        // 将协议改为https
        urlObj.protocol = 'https:'

        // 去掉端口号
        urlObj.port = ''

        return urlObj.toString()
      } catch (error) {
        console.warn('URL转换失败:', error)
        // 如果URL解析失败，使用字符串替换的方式
        return url.replace(/^http:/, 'https:').replace(/:\d+/, '')
      }
    },

    // 新增：处理fxyy数据的方法
    processFxyyData() {
      if (!this.zxJqDetailData || !this.zxJqDetailData.fxyy) {
        return
      }

      const fxyyStr = this.zxJqDetailData.fxyy
      const items = fxyyStr.split(';').filter(item => item.trim())

      const tagMcn = []
      const tagBigV = []
      const tagPersons = []

      items.forEach(item => {
        const parts = item.split('-')
        if (parts.length >= 3) {
          const category = parts[0]
          const field = parts[1]
          const id = parts[2]

          const label = `${field}`

          if (category === 'MCN机构') {
            tagMcn.push({ label, id })
          } else if (category === '重点大V账号') {
            tagBigV.push({ label, id })
          } else if (category === '涉舆重点人员库') {
            tagPersons.push({ label, id })
          }
        }
      })

      this.tagData = {
        tagMcn,
        tagBigV,
        tagPersons
      }
    },
    // 新增：标签点击事件
    handleTagClick(type, id, label) {
      this.currentTagType = type
      this.tagDialogTitle = `${this.getTagTypeLabel(type)} - ${label}`

      let apiCall
      if (type === 'mcn') {
        apiCall = tagMcnDetails({ id })
      } else if (type === 'bigv') {
        apiCall = tagBigVDetails({ id })
      } else if (type === 'persons') {
        apiCall = tagPersonsDetails({ id })
      }

      if (apiCall) {
        apiCall.then(res => {
          this.tagDialogContent = res.data || null
          this.tagDialogVisible = true
        }).catch(err => {
          console.error('获取标签详情失败:', err)
          this.$message.error('获取详情失败')
        })
      }
    },
    // 新增：获取标签类型标签
    getTagTypeLabel(type) {
      const typeMap = {
        mcn: 'MCN机构',
        bigv: '重点大V账号',
        persons: '涉舆重点人员库'
      }
      return typeMap[type] || '未知类型'
    },

    handleBackHome() {
      this.$emit('handleBack', false)
    },
    handleClickIndex(index){
      console.log('click index:', index)
      this.zxJqProcessList = this.zxJqProcessData[index]
      console.log('zxJqProcessList:',this.zxJqProcessList)
    },
    // 根据警情id获取警情详情
    getJqDetails(id) {
      const jjdbh = id
      getJqDetail(jjdbh).then(res => {
        console.log(res)
        this.zxJqDetailData = res.data
        this.processFxyyData()
        this.zxJqResourceData1 = res.data.resourceList.filter(item => item.type == 0)
        console.log(this.zxJqResourceData1)
        this.zxJqResourceData2 = res.data.resourceList.filter(item => item.type == 1)
        console.log(this.zxJqResourceData2)
      }).catch(err => {
        console.log(err)
      })
    },
    // 根据警情id获取警员工号
    getJyghList(id) {
      const jjdbh = id
      getList({instanceId:jjdbh}).then(res => {
        console.log(res)

      }).catch(err => {
        console.log(err)
      })
    },
    getJqProcessData(id) {
      const data = {
        args: {
          instanceId: id
        }
      }
      getJqProcess(data).then(res => {
        console.log(res)
        this.zxJqProcessData = res.data
        this.zxJqProcessList = res.data[0]
        console.log(this.zxJqProcessData)
      }).catch(err => {
        console.log(err)
      })
    },
    handleTableClick(id) {
      console.log(this.jjdbh)
      this.id = id || this.jjdbh
      this.getJqDetails(this.id)
      this.getJqProcessData(this.id)
      this.getJyghList(this.id)
      this.processFxyyData()
      this.videoTabMainIdx = 0
      if (oWebControl != null) {
        oWebControl.JS_HideWnd()
        oWebControl.JS_RequestInterface({
          funcName: 'stopAllPreview'
        })
      }
      this.selectList = []
      this.selectValue = ''
      this.videoSrc = ''
      this.videoImg = ''
      this.videoMp4Show(this.id)
    },
    selectChange(value) {
      console.log(this.selectList[value])
      this.videoSrc = this.selectList[value].videoSrc
      this.videoImg = this.selectList[value].videoImg
    },
    openVideoTabMore() {
      this.isVideoShow = true
      this.videoSrcMore = this.videoSrc
      this.$refs.jkvideo.pause()
    },
    closeVideoTabMore() {
      this.isVideoShow = false
      this.videoSrcMore = ''
    },
    videoTabMainClick(idx) {
      const that = this
      this.videoTabMainIdx = idx
      this.selectValue = ''
      switch (idx) {
        case 0:
          if (oWebControl != null) {
            oWebControl.JS_HideWnd()
          }
          that.videoMp4Show()
          break
          // case 1:
          //   console.log(oWebControl)
          //   that.initPlugin()
          //   that.videoOnlinShow()
          //   break
          // case 2:
          //   if (oWebControl != null) {
          //     oWebControl.JS_HideWnd()
          //   }
          //   break
      }
    },
    videoTabClick(idx) {
      this.videoTabIdx = idx
      this.videoSer(this.videoTabList[idx].value)
    },
    videoSer(id) {
      // oWebControl.JS_RequestInterface({
      //   funcName: 'stopAllPreview'
      // })
      setTimeout(() => {
        console.log(oWebControl)
        oWebControl.JS_RequestInterface({
          funcName: 'startPreview',
          argument: JSON.stringify({
            cameraIndexCode: `${id}`, // 监控点编号
            streamMode: 0, // 主子码流标识
            transMode: 1, // 传输协议
            gpuMode: 0, // 是否开启GPU硬解
            wndId: -1 // 可指定播放窗口
          })
        })
        // let now = new Date()
        // now.setHours(0, 0, 0, 0);
        // oWebControl.JS_RequestInterface({
        //   funcName: 'startPreview',
        //   argument: JSON.stringify({
        //     cameraIndexCode: `${id}`,                   //监控点编号
        //     startTimeStamp: Math.floor(now.getTime() / 1000).toString(),  //录像查询开始时间戳，单位：秒
        //     endTimeStamp: Math.floor(endTimeStamp / 1000).toString(),      //录像结束开始时间戳，单位：秒
        //     recordLocation: 0,                     //录像存储类型：0-中心存储，1-设备存储
        //     transMode: 1,                               //传输协议：0-UDP，1-TCP
        //     gpuMode: 0,                                   //是否启用GPU硬解，0-不启用，1-启用
        //     wndId:-1                                         //可指定播放窗口
        //   })
        // })
      }, 1000)
    },
    // initPlugin() {
    //   const that = this
    //   oWebControl = new WebControl({
    //     szPluginContainer: 'playWnd', // 指定容器id
    //     iServicePortStart: 15900, // 指定起止端口号，建议使用该值
    //     iServicePortEnd: 15900,
    //     szClassId: '23BF3B0A-2C56-4D97-9C03-0CB103AA8F11', // 用于IE10使用ActiveX的clsid
    //     cbConnectSuccess: function() {
    //       // setCallbacks();
    //       // 实例创建成功后需要启动服务
    //       console.log(oWebControl)
    //       oWebControl.JS_StartService('window', {
    //         dllPath: './VideoPluginConnect.dll'
    //       }).then(function() {
    //         oWebControl.JS_SetWindowControlCallback({ // 设置消息回调
    //           cbIntegrationCallBack: that.cbIntegrationCallBack
    //         })
    //
    //         oWebControl.JS_CreateWnd('playWnd', 388, 200).then(function() { // JS_CreateWnd创建视频播放窗口，宽高可设定
    //           console.log('JS_CreateWnd success')
    //           that.init(oWebControl) // 创建播放实例成功后初始化
    //           that.resize(oWebControl)
    //         })
    //       }, function() {
    //
    //       })
    //     },
    //     cbConnectError: function() {
    //       console.log('cbConnectError')
    //       oWebControl = null
    //       $('#playWnd').html('插件未启动，正在尝试启动，请稍候...')
    //       WebControl.JS_WakeUp('VideoWebPlugin://') // 程序未启动时执行error函数，采用wakeup来启动程序
    //       initCount++
    //       if (initCount < 3) {
    //         setTimeout(function() {
    //           this.initPlugin()
    //         }, 3000)
    //       } else {
    //         $('#playWnd').html('插件启动失败，请检查插件是否安装！')
    //       }
    //     },
    //     cbConnectClose: function() {
    //       console.log('cbConnectClose')
    //       oWebControl = null
    //       $('#playWnd').html('插件未启动，正在尝试启动，请稍候...')
    //       WebControl.JS_WakeUp('VideoWebPlugin://')
    //       initCount++
    //       if (initCount < 3) {
    //         setTimeout(function() {
    //           this.initPlugin()
    //         }, 3000)
    //       } else {
    //         $('#playWnd').html('插件启动失败，请检查插件是否安装！')
    //       }
    //     }
    //   })
    // },
    // init(oWebControl) {
    //   const that = this
    //   that.getPubKey(function() {
    //     // //////////////////////////////// 请自行修改以下变量值	////////////////////////////////////
    //     var appkey = `27870667` // 综合安防管理平台提供的appkey，必填
    //     var secret = that.setEncrypt('JytpeVSfWnIlzpLFZpdG') // 综合安防管理平台提供的secret，必填
    //     var ip = '************' // 综合安防管理平台IP地址，必填
    //     var playMode = 0 // 初始播放模式：0-预览，1-回放
    //     var port = 443 // 综合安防管理平台端口，若启用HTTPS协议，默认443
    //     var snapDir = 'D:\\SnapDir' // 抓图存储路径
    //     var videoDir = 'D:\\VideoDir' // 紧急录像或录像剪辑存储路径
    //     var layout = '1x1' // playMode指定模式的布局
    //     var enableHTTPS = 1 // 是否启用HTTPS协议与综合安防管理平台交互，这里总是填1
    //     var encryptedFields = 'secret' // 加密字段，默认加密领域为secret
    //     var showToolbar = 1 // 是否显示工具栏，0-不显示，非0-显示
    //     var showSmart = 1 // 是否显示智能信息（如配置移动侦测后画面上的线框），0-不显示，非0-显示
    //     var buttonIDs = '0,16,256,257,258,259,260,512,513,514,515,516,517,768,769' // 自定义工具条按钮
    //     // var reconnectTimes = 2;                            // 重连次数，回放异常情况下有效
    //     // var reconnectTime = 4;                             // 每次重连的重连间隔 >= reconnectTime
    //     // //////////////////////////////// 请自行修改以上变量值	////////////////////////////////////
    //
    //     oWebControl.JS_RequestInterface({
    //       funcName: 'init',
    //       argument: JSON.stringify({
    //         appkey: appkey, // API网关提供的appkey
    //         secret: secret, // API网关提供的secret
    //         ip: ip, // API网关IP地址
    //         playMode: playMode, // 播放模式（决定显示预览还是回放界面）
    //         port: port, // 端口
    //         snapDir: snapDir, // 抓图存储路径
    //         videoDir: videoDir, // 紧急录像或录像剪辑存储路径
    //         layout: layout, // 布局
    //         enableHTTPS: enableHTTPS, // 是否启用HTTPS协议
    //         encryptedFields: encryptedFields, // 加密字段
    //         showToolbar: showToolbar, // 是否显示工具栏
    //         showSmart: showSmart, // 是否显示智能信息
    //         buttonIDs: buttonIDs // 自定义工具条按钮
    //         // reconnectTimes：reconnectTimes,            //重连次数
    //         // reconnectDuration：reconnectTime           //重连间隔
    //       })
    //     }).then(function(oData) {
    //       console.log('11111' + oData)
    //       console.log(oData)
    //       console.log(oWebControl)
    //       oWebControl.JS_Resize(388, 200) // 初始化后resize一次，规避firefox下首次显示窗口后插件窗口未与DIV窗口重合问题
    //     })
    //   }, oWebControl)
    // },
    // cbIntegrationCallBack(oData) {
    //   console.log(JSON.stringify(oData.responseMsg))
    // },
    // getPubKey(callback, oWebControl) {
    //   oWebControl.JS_RequestInterface({
    //     funcName: 'getRSAPubKey',
    //     argument: JSON.stringify({
    //       keyLength: 1024
    //     })
    //   }).then(function(oData) {
    //     console.log(oData)
    //     if (oData.responseMsg.data) {
    //       pubKey = oData.responseMsg.data
    //       callback()
    //     }
    //   })
    // },
    // setEncrypt(value) {
    //   var encrypt = new JSEncrypt()
    //   encrypt.setPublicKey(pubKey)
    //   return encrypt.encrypt(value)
    // },
    // resize(oWebControl) {
    //   console.log('re')
    //   const that = this
    //   that.setWndCover(oWebControl)
    //
    //   $(window).resize(function() {
    //     if (oWebControl != null) {
    //       oWebControl.JS_Resize(388, 200)
    //       that.setWndCover(oWebControl)
    //     }
    //   })
    // },
    // setWndCover(oWebControl) {
    //   var iWidth = $(window).width()
    //   var iHeight = $(window).height()
    //   var oDivRect = $('#playWnd').get(0).getBoundingClientRect()
    //
    //   var iCoverLeft = (oDivRect.left < 0) ? Math.abs(oDivRect.left) : 0
    //   var iCoverTop = (oDivRect.top < 0) ? Math.abs(oDivRect.top) : 0
    //   var iCoverRight = (oDivRect.right - iWidth > 0) ? Math.round(oDivRect.right - iWidth) : 0
    //   var iCoverBottom = (oDivRect.bottom - iHeight > 0) ? Math.round(oDivRect.bottom - iHeight) : 0
    //
    //   iCoverLeft = (iCoverLeft > 1000) ? 1000 : iCoverLeft
    //   iCoverTop = (iCoverTop > 600) ? 600 : iCoverTop
    //   iCoverRight = (iCoverRight > 1000) ? 1000 : iCoverRight
    //   iCoverBottom = (iCoverBottom > 600) ? 600 : iCoverBottom
    //   console.log(`ic` + iCoverLeft, oDivRect)
    //   oWebControl.JS_RepairPartWindow(0, 0, 300, 600) // 多1个像素点防止还原后边界缺失一个像素条
    //   // oWebControl.JS_CuttingPartWindow(0, 0, 100, 600)
    //   if (iCoverLeft != 0) {
    //     oWebControl.JS_CuttingPartWindow(0, 0, iCoverLeft, 600)
    //   }
    //   if (iCoverTop != 0) {
    //     oWebControl.JS_CuttingPartWindow(0, 0, 1001, iCoverTop) // 多剪掉一个像素条，防止出现剪掉一部分窗口后出现一个像素条
    //   }
    //   if (iCoverRight != 0) {
    //     oWebControl.JS_CuttingPartWindow(1000 - iCoverRight, 0, iCoverRight, 600)
    //   }
    //   if (iCoverBottom != 0) {
    //     oWebControl.JS_CuttingPartWindow(0, 600 - iCoverBottom, 1000, iCoverBottom)
    //   }
    // },
    // 直播视频显示
    // videoOnlinShow() {
    //   const that = this
    //   that.videoTabList = []
    //   // const dt = '12334, 2341234, 66777'
    //   let array = that.row.jygh.split(',')
    //   // let array = dt.split(', ')
    //   array = array.map(item => item.trim())
    //   console.log(array)
    //   // that.processArray(array)
    //   for (let i = 0; i < array.length; i++) {
    //     getDevice({
    //       code: array[i].length < 8 ? `33${array[i]}` : `${array[i]}`
    //     }).then(res => {
    //       console.log(res)
    //       getDevice2({
    //         code: res.data[0].GPSID
    //       }).then(ress => {
    //         console.log(ress)
    //         const obj = {
    //           name: `视频`,
    //           value: `${ress.data[0].NOINFO}`
    //         }
    //         console.log(obj)
    //         that.videoTabList.push(obj)
    //       })
    //     })
    //   }
    //   setTimeout(function() {
    //     console.log('videoTabList:')
    //     console.log(that.videoTabList[0])
    //     if (that.videoTabList[0].value) {
    //       that.videoSer(that.videoTabList[0].value)
    //     } else {
    //       if (oWebControl != null) {
    //         oWebControl.JS_HideWnd()
    //       }
    //     }
    //   }, 1500)
    // },
    // 显示视频录像
    async videoMp4Show(id) {
      const that = this
      that.selectList = []
      that.selectValue = ''
      // console.log(that.row)
      // //望江跳桥
      // if(this.jjdbh=='3301002503065600213'){
      //   that.selectList=[{
      //     name: '2023-09-05 09:29:23',
      //     value: 0,
      //     videoSrc: 'http://41.198.129.83:8888/surveillance_video/f3d1ee7c85af4a97a3d9b26f9d7900af.mp4',
      //     videoImg: ''
      //   }]
      //   that.selectValue = that.selectList[0].name
      //   that.videoSrc = that.selectList[0].videoSrc
      //   that.videoImg = that.selectList[0].videoImg
      //   that.aivideoSrc='http://41.198.129.83:8888/intelligent_synthesis/c14919ab38bc4d4a8fa6d2475abdaf6b.mp4'
      //   return
      // }
      // //凯旋踹门
      // if(that.row.jjdbh=='3301002309055300105'){
      //   that.selectList=[{
      //     name: '2023-09-05 09:29:23',
      //     value: 0,
      //     videoSrc: 'http://41.198.129.83:8888/individual/a182377b272b4f5b9f9d103442d1634c.mp4',
      //     videoImg: ''
      //   }]
      //   that.selectValue = that.selectList[0].name
      //   that.videoSrc = that.selectList[0].videoSrc
      //   that.videoImg = that.selectList[0].videoImg
      //   that.aivideoSrc='http://41.198.129.83:8888/intelligent_synthesis/5280f5b79494497daf1e304f40439404.mp4'
      //   return
      // }
      // //紫阳走失
      // if(that.row.jjdbh=='3301002310115000899'){
      //   that.selectList=[{
      //     name: '2023-10-11 22:24:05',
      //     value: 0,
      //     videoSrc: 'http://41.198.129.83:8888/individual/25bbc5b906804e8ba3dc71c17d977458.mp4',
      //     videoImg: ''
      //   }]
      //   that.selectValue = that.selectList[0].name
      //   that.videoSrc = that.selectList[0].videoSrc
      //   that.videoImg = that.selectList[0].videoImg
      //   that.aivideoSrc='http://41.198.129.83:8888/intelligent_synthesis/d4c519563b714f1384010d91acea889c.mp4'
      //   return
      // }
      //--//
      axios.get(`https://41.198.0.176:9443/data/api/jcjEvidences?authority=8a2618af90452af5ab74b6576ccbff77ec13dabed63fee57b2404d35532bb83484667563d5bfb70c66ed506b45634126&jjdbh=${id}&cjdbh=`).then(response => {
        console.log(response)
        if (response.data.code === 20000) {
          let idx = 0
          if (response.data.data.length > 0) {
            response.data.data.forEach(item => {
              console.log(item)
              if (item.type == 1) {
                const obj = {
                  name: item.shotTime,
                  value: idx,
                  videoSrc: that.convertToHttpsWithoutPort(item.url),
                  videoImg: that.convertToHttpsWithoutPort(item.thumbUrl)
                }
                that.selectList.push(obj)
                idx++
              }
            })
            console.log(that.selectList)
            that.selectValue = that.selectList[0].name
            that.videoSrc = that.selectList[0].videoSrc
            that.videoImg = that.selectList[0].videoImg
          }
        }
      }).catch(error => {
        console.log('VideoError:' + error)
        that.$confirm('请打开链接开启视频权限', '提示', {
          confirmButtonText: '立即打开',
          cancelButtonText: '取消',
          type: 'warning'
        }).then((res) => {
          console.log(res)
          window.open(`https://41.198.0.176:9443/data/api/jcjEvidences?authority=8a2618af90452af5ab74b6576ccbff77ec13dabed63fee57b2404d35532bb83484667563d5bfb70c66ed506b45634126&jjdbh=${id}&cjdbh=`)
        }).catch((error) => {
          console.log(error)
        })
      })
      // const response = { 'code': 20000, 'msg': '成功', 'data': [{ 'jjdbh': '', 'fileId': '', 'fileName': '', 'type': 1, 'url': 'http:/*************:8080/evisys_stream/Files/ctkj23/Extract.%7B208D2C60-3AEA-1069-A2D7-08002B30309D%7D/012906/2750179/202404/Video/HIV_20240408121000_0000_012906_MTP_2750179_NOR.mp4', 'thumbUrl': '封面地址', 'shotTime': '2024年4月9日 11:02:11', 'duration': '', 'serial': '' }, { 'jjdbh': '', 'fileId': '', 'fileName': '', 'type': 1, 'url': 'http:/*************:8080/evisys_stream/Files/ctkj23/Extract.%7B208D2C60-3AEA-1069-A2D7-08002B30309D%7D/012906/2750179/202404/Video/HIV_20240408121000_0000_012906_MTP_2750179_NOR.mp4', 'thumbUrl': '封面地址', 'shotTime': '2024年4月9日 11:02:20', 'duration': '', 'serial': '' }] }

      // that.videoImg = 'https://oss.zjhrnet.com/img/cunliang/liangtong/hebeiliantong/cailing/sh/ba_1.png'
      // that.videoSrc = 'http:/*************:8080/evisys_stream/Files/ctkj23/Extract.%7B208D2C60-3AEA-1069-A2D7-08002B30309D%7D/012906/2750179/202404/Video/HIV_20240408121000_0000_012906_MTP_2750179_NOR.mp4'

      // that.videoSrc = 'https://oss.zjhrnet.com/img/cunliang/liangtong/hebeiliantong/cailing/sh/ct/1.mp4'
      // 'http:/*************:8080/evisys_stream/Files/ctkj23/Extract.%7B208D2C60-3AEA-1069-A2D7-08002B30309D%7D/012906/2750179/202404/Video/HIV_20240408121000_0000_012906_MTP_2750179_NOR.mp4'
    },
  }
}
</script>

<style lang="scss" scoped>
.no-data{
  color: #0bdefd;
}
.container {
  position: relative;
  display: flex;
  justify-content: space-between;
  height: 1040px;
  padding: 33px 20px 18px 20px;
  background: #0F172A;
  .container-title {
    width: 100%;
    height: 31px;
    line-height: 31px;
    // background: url('../../../assets/images/warning-instance/bg-title.png')
    // no-repeat;
    background-size: 100% 100%;
    font-family: PingFangSC, PingFang SC;
    font-weight: 600;
    font-size: 22px;
    // color: #FFFFFF;
    // padding-left: 28px;
    margin-bottom: 10px;
    span {
      background: #FFFFFF;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
  }
  .el-button {
    position: absolute;
    top: 47px;
    right: 776px;
    width: 108px;
    height: 40px;
    border-radius: 22px;
    background: #0F1629;
    border: 1px solid #343947;
    font-weight: 400;
    font-size: 14px;
    display: flex;
    align-items: center;
    justify-content: center;
    .inner{
      display: flex;
      align-items: center;
      justify-content: center;
      img{
        width: 20px;
        height: 20px;
        line-height: 20px;
      }
      // color: #676C77;
      .button-inner {
        line-height: 20px;
        background: #676C77;
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
      }
    }
  }
}

.container-left {
  width: 737px;
  height: 974px;
  margin-top: 15px;
  padding: 22px 24px 35px 24px;
  background: #040D1B;
  border-radius: 10px;
}
.container-center {
  width: calc(100% - 2 * 737px - 36px);
}
.container-right {
  width: 737px;
  height: 974px;
  margin-top: 15px;
  // padding: 22px 24px 35px 24px;
  // background: #040D1B;
  // border-radius: 10px;
}
.container-right-1{
  width: 737px;
  height: 496px;
  padding: 22px 24px;
  background: #040D1B;
  border-radius: 10px;
}
.container-right-2 {
  margin-top: 18px;
  padding: 22px 24px 0 24px;
  background: #040D1B;
  border-radius: 10px;
  height: 457px;
  .video-tab-more{
    height: 500px;
    border: 1px solid rgb(26,82,165);
    margin-top: 20px;
    position: relative;
    .close{
      position: absolute;
      top: 10px;
      right: 10px;
      z-index: 999;
    }
    .video-tab-more-sec{
      display: flex;
      align-items: center;
      height: 100%;
      justify-content: center;
      .video-more{
        width: 100%;
        height: 100%;
      }
      video::-webkit-media-controls-fullscreen-button {
        display: none;
      }
    }
  }
  .video{
    padding-bottom: 24px;
    .video-tab-main{
      //margin-top: 20px;
      font-size: 16px;
      .ul{
        display: flex;
        align-items: center;
        justify-content: space-between;
        .li{
          border: 1px solid rgb(26,82,165);
          background: rgba(0,0,0,.2);
          color: #a7a6a6;
          padding: 8px 20px;
          cursor: pointer;
          //margin-right: 20px;
        }
        .active{
          background: rgba(16,132,227,.7);
          color: #fff;
        }
      }
    }
    .video-tab{
      margin-top: 10px;
      .ul{
        display: flex;
        align-items: center;
        justify-content: space-between;
        .li{
          border: 1px solid rgb(26,82,165);
          background: rgba(0,0,0,.2);
          color: #a7a6a6;
          padding: 8px 30px;
          cursor: pointer;
        }
        .active{
          background: rgba(16,132,227,.7);
          color: #fff;
        }
      }
      .tab-select{
        ::v-deep .el-input__inner{
          background: none;
          color: #fff;
        }
      }
    }
    .video-sec-1{
      margin-top: 10px;
      padding: 24px 25px;
      height: 270px;
      border: 1px solid rgb(26,82,165);
      display: flex;
      align-items: center;
      justify-content: center;
      position: relative;
      overflow-y: auto;
      .video{
        // width: 100%;
        // height: 100%;
        width: 687px;
        height: 231px;
      }
      .video-sec-fd{
        position: absolute;
        bottom: 10px;
        right: 10px;
        z-index: 999;
      }
      video::-webkit-media-controls-fullscreen-button {
        display: none;
      }
    }
    .video-sec{
      margin-top: 20px;
      height: 200px;
      border: 1px solid rgb(26,82,165);
      display: flex;
      align-items: center;
      justify-content: center;
      position: relative;
      .video-sec-more{
        position: absolute;
        /*bottom: 10px;
        right: 10px;*/
        top: 50%;
        left: 50%;
        transform: translate(-50%,-50%);
      }

    }
  }
}
.tag-group-1 {
  margin-top: 14px;
  display: flex;
  gap: 8px;
  align-items: center;
}
.sec {
  margin-top: 14px;
  font-size: 16px;
  //background: rgb(248, 248, 248);
  padding: 10px;
  height: 360px;
  border-radius: 6px;

  .ul {
    display: flex;
    align-items: center;
    flex-wrap: wrap;

    .li {
      min-width: 45%;
      display: flex;
      align-items: center;
      margin-bottom: 25px;

      .t {
        font-size: 12px;
        color: #fff;
        width: 50px;
      }

      .d {
        color: #fff;
      }
    }

    .time {
      width: 100%;
    }
  }
}

// 自定义对话框样式
::v-deep .custom-dialog {
  background: #040D1B;
  border: 1px solid #343947;
  border-radius: 10px;

  .el-dialog__header {
    background: #040D1B;
    border-bottom: 1px solid #343947;
    padding: 20px 24px;

    .el-dialog__title {
      color: #FFFFFF;
      font-size: 18px;
      font-weight: 600;
    }

    .el-dialog__close {
      color: #676C77;
      font-size: 20px;

      &:hover {
        color: #FFFFFF;
      }
    }
  }

  .el-dialog__body {
    background: #040D1B;
    padding: 24px;
    color: #FFFFFF;
  }

  .el-dialog__footer {
    background: #040D1B;
    border-top: 1px solid #343947;
    padding: 20px 24px;

    .el-button {
      background: #0F1629;
      border: 1px solid #343947;
      color: #676C77;

      &:hover {
        background: #1A2332;
        border-color: #4A5568;
        color: #FFFFFF;
      }
    }
  }
}

// 自定义描述列表样式 - 使用更强的选择器优先级和!important
.tag-detail-content {
  ::v-deep .el-descriptions {
    .el-descriptions__header {
      .el-descriptions__title {
        color: #FFFFFF !important;
        font-size: 16px !important;
        font-weight: 600 !important;
      }
    }

    .el-descriptions__body {
      .el-descriptions__table {
        border-color: #343947 !important;
        background: transparent !important;

        .el-descriptions__cell {
          border: 1px solid #343947 !important;

          &.is-bordered-label {
            background-color: #0F1629 !important;
            color: #0BDEFD !important;
            font-weight: 500 !important;
            padding: 12px 16px !important;
            border-color: #343947 !important;
          }

          &.is-bordered-content {
            background-color: #040D1B !important;
            color: #FFFFFF !important;
            padding: 12px 16px !important;
            border-color: #343947 !important;
          }
        }

        tr {
          background: transparent !important;

          td {
            background: transparent !important;
            border-color: #343947 !important;
          }

          th {
            background: #0F1629 !important;
            color: #0BDEFD !important;
            border-color: #343947 !important;
          }
        }
      }
    }
  }
}
</style>

<style lang="scss">
// 全局样式覆盖 - 使用最高优先级确保样式生效
.el-dialog.custom-dialog {
  background: #040D1B !important;
  border: 1px solid #343947 !important;

  .el-dialog__header {
    background: #040D1B !important;
    border-bottom: 1px solid #343947 !important;

    .el-dialog__title {
      color: #FFFFFF !important;
    }

    .el-dialog__close {
      color: #676C77 !important;

      &:hover {
        color: #FFFFFF !important;
      }
    }
  }

  .el-dialog__body {
    background: #040D1B !important;
    color: #FFFFFF !important;

    .el-descriptions {
      .el-descriptions__header .el-descriptions__title {
        color: #FFFFFF !important;
      }

      .el-descriptions__body {
        .el-descriptions__table {
          border-collapse: collapse !important;
          border: 1px solid #343947 !important;
          background: transparent !important;

          .el-descriptions__cell {
            border: 1px solid #343947 !important;

            &.is-bordered-label {
              background-color: #0F1629 !important;
              color: #0BDEFD !important;
              font-weight: 500 !important;
              padding: 12px 16px !important;
            }

            &.is-bordered-content {
              background-color: #040D1B !important;
              color: #FFFFFF !important;
              padding: 12px 16px !important;
            }
          }

          tr {
            background: transparent !important;

            td {
              background-color: #040D1B !important;
              color: #FFFFFF !important;
              border: 1px solid #343947 !important;
            }

            th {
              background-color: #0F1629 !important;
              color: #0BDEFD !important;
              border: 1px solid #343947 !important;
            }
          }
        }
      }
    }
  }

  .el-dialog__footer {
    background: #040D1B !important;
    border-top: 1px solid #343947 !important;

    .el-button {
      background: #0F1629 !important;
      border: 1px solid #343947 !important;
      color: #676C77 !important;

      &:hover {
        background: #1A2332 !important;
        border-color: #4A5568 !important;
        color: #FFFFFF !important;
      }
    }
  }
}

// 额外的全局覆盖，确保所有情况下都生效
.el-descriptions__table {
  .el-descriptions__cell.is-bordered-label {
    background-color: #0F1629 !important;
    color: #0BDEFD !important;
  }

  .el-descriptions__cell.is-bordered-content {
    background-color: #040D1B !important;
    color: #FFFFFF !important;
  }
}
</style>

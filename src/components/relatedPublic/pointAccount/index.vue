<template>
  <div>
    <div class="container">
      <div class="container-button">
        <i class="el-icon-close" style="cursor: pointer;" @click="handleBack"></i>
      </div>
<!--      <div style="position: absolute; left: 53.8%; top: 54px; z-index: *************; font-family: ShangShouJiangHuShuFaTi-2; font-weight: 400; color: #c5e8f6; font-size: 32px; letter-spacing: 3px">-重点账号</div>-->
      <div class="container-box">
        <div class="container-left">
          <div class="user-info">
            <div class="user-avatar">
              <img
                src="../../../assets/images/point-account/default-avatar.png"
                alt=""
              >
            </div>
            <div class="user-desc">
              <div class="user-desc-item">
                <span class="user-desc-item-label">账号</span>
                <span class="user-desc-item-value">{{ item.nikeName }}</span>
              </div>
              <div class="user-desc-item">
                <span class="user-desc-item-label">账号等级</span>
                <span class="user-desc-item-value">{{ item.userLevel === '1' ? '重点' : item.userLevel === '2' ? '敏感' : '未知' }}</span>
              </div>
              <div class="user-desc-item">
                <span class="user-desc-item-label">用户分类</span>
                <span class="user-desc-item-value">{{ item.userType === '1' ? '个人' : item.userType === '2' ? '媒体' : item.userType === '3' ? '企事业单位' : '未知' }}</span>
              </div>
              <div class="user-desc-item">
                <span class="user-desc-item-label">备注</span>
                <span class="user-desc-item-value">{{ item.desc }}</span>
              </div>
            </div>
          </div>
          <div class="user-account-1">
<!--            <img-->
<!--              src="../../../assets/images/point-account/icon-arrow.png"-->
<!--              alt=""-->
<!--              class="icon-arrow"-->
<!--            >-->
            <div class="user-account-header">
              <span>其他账号</span>
            </div>
            <div class="user-account-list">
              <div class="user-account-list-header">
                <el-col width="135px" class="list-header-left" >平台</el-col>
                <el-col  class="list-header-right">账号名</el-col>
              </div>
              <div class="user-account-list-container">
                <div
                  v-for="account in emphasizeUserData"
                  :key="account.id"
                  class="user-account-list-item"
                >
                  <el-col style="width:135px;paddingLeft:14px;" class="list-item-left">
<!--                    <img :src="account.platformIcon" alt="">-->
                    <span>{{ account.platform }}</span>
                  </el-col>
                  <el-col class="list-item-right" style="paddingLeft:0px;">{{
                    account.nikeName
                  }}</el-col>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="container-right">
          <div class="header-left">
            <span>发布内容</span>
          </div>
          <el-select
            v-model="platformValue"
            placeholder="平台选择"
            :popper-append-to-body="false"
            @change="handlePlatformChange"
          >
            <el-option
              v-for="item in platformOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
          <div class="container-table">
            <el-table :data="emphasizeActData" style="width: 100%">
              <el-table-column prop="title" label="标题" width="180">
                <template #default="scope">
                  <div class="ellipsis-2-lines">
                    {{ scope.row.title }}
                  </div>
                </template>
              </el-table-column>
              <el-table-column prop="content" label="详情" width="300">
                <template #default="scope">
                  <div class="ellipsis-2-lines">
                    {{ scope.row.content }}
                  </div>
                </template>
              </el-table-column>
<!--              <el-table-column label="涉杭">-->
<!--                <template slot-scope="scope">-->
<!--                  {{ scope.row.isAsianPolice === '1' ? '是' : scope.row.isAsianPolice === '0' ? '否' : '未知' }}-->
<!--                </template>-->
<!--              </el-table-column>-->
              <el-table-column prop="webName" label="平台" />
              <el-table-column label="涉警">
                <template slot-scope="scope">
                  {{ scope.row.isPolice === '1' ? '是' : scope.row.isPolice === '0' ? '否' : '未知' }}
                </template>
              </el-table-column>
              <el-table-column prop="publishTime" label="时间"/>
            </el-table>
            <div class="container-pagination">
              <el-pagination
                background
                :current-page="currentPage"
                :page-size="pageSize"
                layout="prev, pager, next"
                :total="total"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import iconWb from '@/assets/images/point-account/icon-wb.png'
import iconWx from '@/assets/images/point-account/icon-wx.png'
import iconDy from '@/assets/images/point-account/icon-dy.png'
export default {
  props: {
    emphasizeActData: Array,
    emphasizeUserData: Array,
    pointAccountPagination: Object,
    platformOptions: Array,
    item: Object
  },
  data() {
    return {
      accountList: [
        {
          id: 1,
          platform: '微博',
          platformIcon: iconWb,
          accountName: '张三的微博'
        },
        {
          id: 2,
          platform: '微信公众号',
          platformIcon: iconWx,
          accountName: '张三的微信公众号'
        },
        {
          id: 3,
          platform: '抖音',
          platformIcon: iconDy,
          accountName: '张三的抖音'
        },
        {
          id: 4,
          platform: '抖音',
          platformIcon: iconDy,
          accountName: '张三的抖音'
        },
        {
          id: 5,
          platform: '抖音',
          platformIcon: iconDy,
          accountName: '张三的抖音'
        }
      ],
      // platformOptions: [
      //   {
      //     value: '',
      //     label: '全部'
      //   }, {
      //     value: '微博',
      //     label: '微博'
      //   }, {
      //     value: '抖音',
      //     label: '抖音'
      //   }, {
      //     value: '小红书',
      //     label: '小红书'
      //   }, {
      //     value: 'B站',
      //     label: 'B站'
      //   }, {
      //     value: '快手',
      //     label: '快手'
      //   }, {
      //     value: '头条',
      //     label: '头条'
      //   }
      // ],
      platformValue: '',
      tableData: [
        {
          time: '10-23 10:33:23',
          police: '否',
          hang: '否',
          title: '网传xx地存在 xx事件',
          platform: '微信公众号',
          detail: '反馈内容:4月7日，一则“武警将接...'
        },
        {
          time: '10-23 10:33:23',
          police: '否',
          hang: '否',
          title: '网传xx地存在 xx事件',
          platform: '微信公众号',
          detail: '反馈内容:4月7日，一则“武警将接...'
        },
        {
          time: '10-23 10:33:23',
          police: '否',
          hang: '否',
          title: '网传xx地存在 xx事件',
          platform: '微信公众号',
          detail: '反馈内容:4月7日，一则“武警将接...'
        },
        {
          time: '10-23 10:33:23',
          police: '否',
          hang: '否',
          title: '网传xx地存在 xx事件',
          platform: '微信公众号',
          detail: '反馈内容:4月7日，一则“武警将接...'
        },
        {
          time: '10-23 10:33:23',
          police: '否',
          hang: '否',
          title: '网传xx地存在 xx事件',
          platform: '微信公众号',
          detail: '反馈内容:4月7日，一则“武警将接...'
        },
        {
          time: '10-23 10:33:23',
          police: '否',
          hang: '否',
          title: '网传xx地存在 xx事件',
          platform: '微信公众号',
          detail: '反馈内容:4月7日，一则“武警将接...'
        },
        {
          time: '10-23 10:33:23',
          police: '否',
          hang: '否',
          title: '网传xx地存在 xx事件',
          platform: '微信公众号',
          detail: '反馈内容:4月7日，一则“武警将接...'
        },
        {
          time: '10-23 10:33:23',
          police: '否',
          hang: '否',
          title: '网传xx地存在 xx事件',
          platform: '微信公众号',
          detail: '反馈内容:4月7日，一则“武警将接...'
        },
        {
          time: '10-23 10:33:23',
          police: '否',
          hang: '否',
          title: '网传xx地存在 xx事件',
          platform: '微信公众号',
          detail: '反馈内容:4月7日，一则“武警将接...'
        },
        {
          time: '10-23 10:33:23',
          police: '否',
          hang: '否',
          title: '网传xx地存在 xx事件',
          platform: '微信公众号',
          detail: '反馈内容:4月7日，一则“武警将接...'
        }
      ],
      currentPage: 1,
      pageSize:10,
      total:10
    }
  },
  methods: {
    handleBack() {
      this.$emit('handleBack', false)
    },
    handleSizeChange(val) {
      this.pageSize = val;
      this.emitPaginationChange();
    },
    handleCurrentChange(val) {
      this.currentPage = val;
      this.emitPaginationChange();
    },
    handlePlatformChange(val) {
      this.$emit('platformChange', val)
    },
    emitPaginationChange() {
      this.$emit('paginationChange', {
        pageNumber: this.currentPage,
        pageSize: this.pageSize,
      });
    },
  },
  // 监听父组件传过来的数据变化
  watch: {
    emphasizeActData: {
      handler(newVal, oldVal) {
        console.log('emphasizeActData changed:', newVal, oldVal);
      },
      deep: true,
    },
    emphasizeUserData: {
      handler(newVal, oldVal) {
        console.log('emphasizeUserData changed:', newVal, oldVal);
      },
      deep: true,
    },
    pointAccountPagination: {
      handler(newVal) {
        if (newVal && newVal.pageNumber && newVal.pageSize) {
          this.currentPage = newVal.pageNumber;
          this.pageSize = newVal.pageSize;
          this.total = newVal.totalNum;
        } else {
          console.warn('Invalid pointAccountPagination object:', newVal);
        }
      },
      deep: true,
      // immediate: true
    },
    platformOptions: {
      handler(newVal, oldVal) {
        console.log('platformOptions changed:', newVal, oldVal);
      },
      immediate: true,
      deep: true
    },
    item: {
      handler(newVal, oldVal) {
        console.log('item changed:', newVal, oldVal);
      },
      deep: true,
    }
  },
  mounted() {
    // console.log(item)
  }
}
</script>

<style lang="scss" scoped>
.ellipsis-2-lines {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
.container {
  width: 1490px;
  height: 1040px;
  background: rgba(3,13,28,0.9);
  backdrop-filter: blur(50px);
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  margin: auto;
  z-index: 9999;
}
.container-button {
  position: relative;
  top: 20px;
  left: -35px;

  .el-icon-close{
    width: 20px;
    height: 20px;
    color: rgba($color: #fff, $alpha: 0.6);
  }

}
.container-box {
  position: absolute;
  top: 0;
  display: flex;
  align-items: center;
  width: 100%;
  height: 100%;
  .container-left {
    width: 447px;
    height: 100%;
    padding: 68px 24px 0px 20px;

  }
  .container-right {
    width: 1043px;
    height: 1040px;
    background: rgba($color: #fff, $alpha: 0.07);
    padding: 0px 26px 0px 24px;
      .header-left {
        width: 80px;
        height: 31px;
        font-family: PingFangSC, PingFang SC;
        font-weight: 600;
        font-size: 20px;
        color: #FFFFFF;
        line-height: 31px;
        text-align: left;
        font-style: normal;
        margin-top: 27px;
      }

      .el-select {
        width: 125px !important;
        height: 36px !important;
        border-radius: 6px !important;
        opacity: 0.6;
        position: absolute;
        top: 24px;
        right: 26px;
        z-index: 999;
      }
      .el-select .el-input__icon {
        line-height: 36px !important;
      }

      .el-select .el-input{
        height: 36px;
      }
      :deep(.el-input__inner){
        height: 36px !important;
        background-color: transparent !important;
        border-radius: 6px;
        border: 1px solid #FFFFFF;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: 16px;
        color: #FFFFFF !important;
        line-height: 31px;
        text-align: left;
        font-style: normal;
      }
      :deep(.el-input__suffix){
        margin-top: 3px;
      }

    .container-table {
      position: absolute;
      width: 993px;
      top:78px;
      z-index: 99;
    }
  }
}

// ::v-deep .el-table th.el-table__cell > .cell {
//   color: #71cdf9;
//   background: linear-gradient(0deg, #46aef7 0%, #1dd5e6 100%);
//   -webkit-background-clip: text;
//   -webkit-text-fill-color: transparent;
// }
</style>
<style lang="scss">
.container-table {
  .el-table{
    margin-bottom: 30px;
  }
  /*最外层透明*/
  .el-table::before {
    display: none;
  }
  .el-table,
  .el-table__expanded-cell {
    background-color: transparent;
    color: #ffffff;
    font-size: 15px;
  }
  /* 表格内背景颜色 */
  .el-table tr,
  .el-table td {
    background-color: transparent;
  }
  .el-table thead .el-table__cell{
    background: rgba(255,255,255,0.06) !important;
    height: 40px !important;
    color: rgba($color: #fff, $alpha: 0.5) !important;
    border-top: 1px solid rgba($color: #fff, $alpha: 0.2) !important;
    border-bottom: 1px solid rgba($color: #fff, $alpha: 0.2) !important;
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    font-size: 15px;
    line-height: 21px;
    text-align: left;
    font-style: normal;
    &:first-of-type{
      border-left: 1px solid rgba($color: #fff, $alpha: 0.2) !important;
    }
    &:last-of-type{
      border-right: 1px solid rgba($color: #fff, $alpha: 0.2) !important;
      text-align: right;
      padding-right: 20px;
    }
  }

  .el-table .el-table__body .el-table__cell{
    background: rgba(255,255,255,0.02) !important;
    height: 54px;
    border-bottom: 1px solid rgba(255,255,255,0.1);
    padding: 0;
    white-space: nowrap;
    text-overflow: ellipsis;
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    font-size: 15px;
    color: rgba($color: #fff, $alpha: 0.5);
    line-height: 21px;
    text-align: left;
    font-style: normal;
    &:first-of-type{
      border-left: 1px solid rgba($color: #fff, $alpha: 0.1) !important;
    }
    &:last-of-type{
      border-right: 1px solid rgba($color: #fff, $alpha: 0.1) !important;
    }
  }
}
.user-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 30px;
  .user-avatar {
    width: 98px;
    height: 98px;
    background: #FFFFFF;
    border-radius: 64px;
    img {
      display: block;
      width: 100%;
      height: 100%;
    }
  }
  .user-desc {
    height: 166px;
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    font-size: 16px;
    color: rgba($color: #fff, $alpha: 0.7);
    text-align: right;
    font-style: normal;
    width: 403px;
    height: 166px;
    margin-top: 33px;
    .user-desc-item {
      height: 44px;
      width: 100%;
      border-bottom: 1px solid rgba($color: #fff, $alpha: 0.1);
      display: flex;
      justify-content: space-between;
      align-items: center;
      box-sizing: border-box;
      line-height: 44px;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      .user-desc-item-label{
        margin-right: 40px;
      }
      .user-desc-item-value{
        color: #fff;
      }
    }
  }
}
.user-account-1 {
  width: 405px;
  height: 303px;
  margin-top: 30px;
  display: flex;
  flex-direction: column;
  .user-account-header {
    display: flex;
    align-items: center;
    width: 80px !important;
    height: 31px !important;
    font-family: PingFangSC, PingFang SC;
    font-weight: 600;
    font-size: 20px;
    color: #FFFFFF;
    line-height: 31px;
    text-align: left;
    font-style: normal;
    background: none !important;
    padding: 0 !important;
    span{
      display: block;
      width: 100%;
      height: 100%;
    }
  }
  .user-account-list{
    margin-top: 12px;
  }
  .user-account-list-header {
    display: flex;
    align-items: center;
    width: 405px;
    height: 40px;
    background: rgba(255,255,255,0.06);
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    font-size: 15px;
    color: rgba($color: #fff, $alpha: 0.7);
    line-height: 21px;
    text-align: left;
    font-style: normal;
    border: 1px solid rgba($color: #fff, $alpha: 0.1);
    .list-header-left {
      padding-left: 14px;
    }
    .list-header-right {
      padding-left: 0px;
    }
  }
.user-account-list-item{
    width: 405px;
    height: 44px;
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    font-size: 15px;
    color: rgba($color: #fff, $alpha: 0.7);
    line-height: 44px;
    text-align: left;
    font-style: normal;
    background: rgba(3,13,28,0.9);
    border: 1px solid rgba($color: #fff, $alpha: 0.1);
    .list-item-left{
      padding-left: 14px;
    }
    .list-item-right {
      padding-left: 0px;
    }
  }
}
.container-pagination {
  right: 26px;
  display: flex;
  justify-content: center;
}
</style>
<style lang="scss" scoped>
.container-right {
  z-index: inherit;
  .el-select-dropdown {
    z-index: inherit !important;
    border: none;
    &:hover{
      cursor: pointer;
    }
  //   .el-select-dropdown__list {
  //     background: #fff;
  //     color: black;
  //     // box-shadow: 0px 1px 24px 5px rgba(0, 0, 0, 0.48);
  //     border-radius: 5px;
  //     // border: 1px solid rgba($color: #fff, $alpha: 0.1);
  //     &:hover{
  //       cursor: pointer;
  //     }
  //   }
  //   .el-select-dropdown__item {
  //     &:hover{
  //       cursor: pointer;
  //       background-color: red;
  //       color: #fff;
  //     }
  //     // &.selected,
  //     // &.hover{
  //     //   cursor: pointer !important;
  //     //   color: black;
  //     //   background: red;
  //     // }
  //     // span {
  //     //   background: linear-gradient(0deg, #191819 0%, #ffffff 100%);
  //     //   -webkit-background-clip: text;
  //     //   -webkit-text-fill-color: transparent;
  //     // }
  //   }
  // }
  // :deep(el-popper){
  //   position: relative !important;
  //   z-index: 99999;
  //   &:hover{
  //     cursor: pointer;
  //   }
  }
}



.container-pagination {
  .el-pagination {
    &.is-background .btn-next,
    &.is-background .btn-prev,
    &.is-background .el-pager li {
      width: 34px !important;
      height: 34px !important;
      line-height: 34px;
      background: rgba(57, 62, 74, 0.8) !important;
      box-shadow: 0px 2px 5px 0px rgba(0, 0, 0, 0.1) !important;
      border-radius: 6px !important;
      backdrop-filter: blur(50px) !important;
      color: rgba($color: #fff, $alpha: 0.7) !important;
      margin-top: -2px !important;
      &.active {
        color: #00adfd !important;
      }
    }

  }
}
</style>

/**
 * 响应式样式文件
 * 用于处理不同屏幕尺寸下的布局适配
 */

// CSS变量定义
:root {
  // 默认原始尺寸变量
  --scale-x: 1;
  --scale-y: 1;
  --left-width: 757px;
  --right-width: 737px;
  --center-width: 1526px;
  --screen-width: 3040px;
  --screen-height: 1040px;
  
  // 组件尺寸变量
  --component-width: 737px;
  --component-height: 314px;
  --component-small-height: 192px;
  --component-large-height: 371px;
}

// 大屏幕样式 (>=2560px) - 使用原始设计
.screen-large {
  .container-home {
    width: 3040px;
    height: 1040px;
    
    .container-left {
      width: 757px;
      height: 1040px;
    }
    
    .container-right {
      width: 737px;
      height: 1040px;
    }
    
    .smartpolice {
      left: 757px;
      width: 737px;
    }
    
    .videopreview {
      left: 1529px;
      width: 737px;
      height: 314px;
    }
    
    .switchinterface {
      left: 2061px;
    }
  }
}

// 中等屏幕样式 (1920px-2559px) - 响应式布局
.screen-medium {
  .container-home {
    width: 100vw;
    height: 100vh;
    min-width: 1920px;
    min-height: 1080px;
    
    .container-left {
      width: var(--left-width);
      height: 100vh;
      
      // 左侧组件适配
      .daily-situation,
      .public-opinion-classification,
      .public-opinion-category {
        width: calc(var(--left-width) - 40px);
        margin-left: 20px;
        margin-right: 20px;
      }
    }
    
    .container-right {
      width: var(--right-width);
      height: 100vh;
      padding-right: 20px;
      
      // 右侧组件适配
      .editable-material,
      .media-account,
      .publicize-check {
        width: calc(var(--right-width) - 40px);
      }
    }
    
    .smartpolice {
      left: var(--left-width);
      width: calc(var(--center-width) * 0.5);
      
      .container {
        width: calc(var(--center-width) * 0.5 - 40px);
      }
    }
    
    .videopreview {
      left: calc(var(--left-width) + var(--center-width) * 0.5);
      width: calc(var(--center-width) * 0.5);
      
      .dynamic-border {
        width: calc(var(--center-width) * 0.5 - 40px);
      }
    }
    
    .switchinterface {
      left: calc(100vw - 224px);
      bottom: 367px;
    }
  }
}

// 小屏幕样式 (<=1919px) - 紧凑布局
.screen-small {
  .container-home {
    width: 100vw;
    height: 100vh;
    min-width: 1366px;
    min-height: 768px;
    
    .container-left {
      width: 380px;
      height: 100vh;
      
      .daily-situation,
      .public-opinion-classification,
      .public-opinion-category {
        width: 340px;
        margin-left: 20px;
        height: auto;
        min-height: 150px;
      }
    }
    
    .container-right {
      width: 380px;
      height: 100vh;
      
      .editable-material,
      .media-account,
      .publicize-check {
        width: 340px;
        height: auto;
        min-height: 200px;
      }
    }
    
    .smartpolice {
      left: 380px;
      width: 480px;
      
      .container {
        width: 440px;
        height: 250px;
      }
    }
    
    .videopreview {
      left: 860px;
      width: 480px;
      
      .dynamic-border {
        width: 440px;
        height: 250px;
      }
    }
    
    .switchinterface {
      left: calc(100vw - 204px);
      bottom: 300px;
    }
  }
}

// 通用响应式组件样式
.responsive-component {
  transition: all 0.3s ease-in-out;
  
  // 根据屏幕类型调整字体大小
  .screen-large & {
    font-size: 1rem;
  }
  
  .screen-medium & {
    font-size: 0.9rem;
  }
  
  .screen-small & {
    font-size: 0.8rem;
  }
}

// 响应式表格
.responsive-table {
  .screen-medium &,
  .screen-small & {
    font-size: 12px;
    
    .el-table__cell {
      padding: 8px 0;
    }
  }
}

// 响应式图表容器
.responsive-chart {
  width: 100%;
  height: 100%;
  
  .screen-medium & {
    transform: scale(0.85);
    transform-origin: top left;
  }
  
  .screen-small & {
    transform: scale(0.7);
    transform-origin: top left;
  }
}

// 响应式按钮组
.responsive-button-group {
  .screen-small & {
    .el-button {
      padding: 6px 12px;
      font-size: 12px;
    }
  }
}

// 响应式标题
.responsive-title {
  .screen-large & {
    font-size: 22px;
  }
  
  .screen-medium & {
    font-size: 20px;
  }
  
  .screen-small & {
    font-size: 18px;
  }
}

// 媒体查询补充
@media screen and (max-width: 1919px) {
  html {
    font-size: 16px;
  }
  
  .container-home {
    transform-origin: top left;
  }
}

@media screen and (min-width: 1920px) and (max-width: 2559px) {
  html {
    font-size: 18px;
  }
}

@media screen and (min-width: 2560px) {
  html {
    font-size: 20px;
  }
}

// 响应式工具类
.w-responsive {
  width: var(--component-width);
}

.h-responsive {
  height: var(--component-height);
}

.h-responsive-small {
  height: var(--component-small-height);
}

.h-responsive-large {
  height: var(--component-large-height);
}

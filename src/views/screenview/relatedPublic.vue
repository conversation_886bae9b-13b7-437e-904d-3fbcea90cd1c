<template>
  <div class="container-home">
    <div class="common-header" style="pointer-events: none">
      <commonHeader :show-home-page="showHomePage" @handleBack="handleBack" />
    </div>
    <div v-if="showHomePage" class="map">
      <!--      TODO: 打包之前解开注释-->
       <centerMap />
    </div>

    <div v-if="showHomePage" v-show="showMapPop" class="map-pop">
      <mapPopup ref="mapPopup" @changeVisible="showMapPop = !showMapPop" />
    </div>
    <div v-if="!showMapPop" class="map-pop1">
      <div class="popup-header">
        <div
          :class="['header-button-item', 'header-button-item-1']"
          @click="showMapPopup"
        >
          <!-- <span class="header-button-text">展开</span> -->
          <img src="../../assets/images/unfold.png" alt="" />
        </div>
      </div>
    </div>
    <div v-if="showHomePage" class="container-left">
      <!-- 当日情况 -->
      <dailySituation
        v-if="childMounted"
        :daily-data="dailyData"
        @click.native="handleDailySituationClick($event)"
        style="cursor: pointer"
      />
      <!-- 舆情分级 -->
      <publicOpinionCassification
        v-if="childMounted"
        :opinion-level-data="opinionLevelData"
        @barClick="handleBarClick"
        style="cursor: pointer"
      />
      <!-- 舆情类别 -->
      <publicOpinionCategory
        v-if="childMounted"
        :opinion-category-data="opinionCategoryData"
        @categoryClick="handleCategoryClick"
      />
      <!--        @click.native="handleComponentClick('publicOpinionCategory')"-->
    </div>
    <!-- 智警管理 -->
    <div v-if="showHomePage" class="smartpolice">
      <smartPoliceManagement
        v-if="childMounted"
        :smart-police-data="smartPoliceData"
      />
      <!-- @click.native="handleComponentClick('smartPoliceManagement')" -->
    </div>
    <!--      视频预览-->
    <div v-if="showHomePage" class="videopreview">
      <VideoPreview />
    </div>
    <!-- 切换涉舆/正宣界面按钮 -->
    <div v-if="showHomePage" class="switchinterface">
      <switchInterface
        :show-home-page="showHomePage"
        @handleBack="handleBack"
      />
    </div>
    <div v-if="showHomePage" class="container-right">
      <div class="right_1">
        <div class="popup-header">
          <div class="today_tit">{{ rightTopIndex === 1 ? '敏感警情' : rightTopIndex === 2 ? '人力情报' : '批示流转' }}</div>
          <div class="header-button-group">
            <div
              :class="[
                'header-button-item',
                'header-button-item-1',
                rightTopIndex == 1 ? 'header-button-active' : '',
              ]"
              @click="handleRightTopClick(1)"
            >
              <span class="header-button-text">敏感警情</span>
            </div>
            <div
              :class="[
                'header-button-item',
                'header-button-item-2',
                rightTopIndex == 2 ? 'header-button-active' : '',
              ]"
              @click="handleRightTopClick(2)"
            >
              <span class="header-button-text">人力情报</span>
            </div>
            <div
              :class="[
                'header-button-item',
                'header-button-item-2',
                rightTopIndex == 3 ? 'header-button-active' : '',
              ]"
              @click="handleRightTopClick(3)"
            >
              <span class="header-button-text">批示流转</span>
            </div>
          </div>
        </div>
        <div class="scroll_table">
          <point
            v-if="childMounted && rightTopIndex === 1"
            :district-list="districtList"
            @row-click="handleRowClick2"
            :point-instance-data="pointInstanceData"
          />
          <sentinelSituation
            v-if="sentinelSituationData && rightTopIndex === 2"
            :dept-info-list="deptInfoList"
            :sentinel-situation-data="sentinelSituationData"
            @searchContent="handleSearchContent"
            @toCurrentPage="handleCurrentPage"
          />
          <instruction
            v-if="instructionsData && rightTopIndex === 3"
            :instructions-data="instructionsData"
          />
        </div>
      </div>
      <div class="right_2">
        <div class="popup-header">
          <div class="today_tit">专题监测</div>
          <div class="header-button-group">
            <div
              :class="[
                'header-button-item',
                'header-button-item-1',
                pageIndex == 1 ? 'header-button-active' : '',
              ]"
              @click="handleBUttonClick(1)"
            >
              <span class="header-button-text">重点处置</span>
            </div>
            <div
              :class="[
                'header-button-item',
                'header-button-item-2',
                pageIndex == 2 ? 'header-button-active' : '',
              ]"
              @click="handleBUttonClick(2)"
            >
              <span class="header-button-text">一般关注</span>
            </div>
          </div>
        </div>
        <div class="scroll_table">
          <keyEventsTable
            :key-events-table-data1="keyEventsTableData1"
            :key-events-table-data2="keyEventsTableData2"
            :type="pageIndex"
            @row-click="handleRowClick"
          />
        </div>
      </div>
      <div class="right_3">
        <div class="today_tit">重点账号</div>
        <div>
          <wordCloud
            v-if="childMounted"
            :word-cloud-data="wordCloudData"
            @dataInfo="handleDataInfo"
          />
        </div>
      </div>
      <!-- 人力情报 -->
      <!-- <sentinelSituation
        v-if="sentinelSituationData"
        :dept-info-list="deptInfoList"
        :sentinel-situation-data="sentinelSituationData"
        @searchContent="handleSearchContent"
        @toCurrentPage="handleCurrentPage"
      /> -->
    </div>

    <pointAccount
      v-if="showHomePage && showPointAccount"
      :emphasize-act-data="emphasizeActMsgData"
      :emphasize-user-data="emphasizeUserListData"
      :item="item"
      :platform-options="platformOptions"
      :point-account-pagination="pointAccountPagination"
      @handleBack="handleBack"
      @paginationChange="updatePagination"
      @platformChange="handlePlatformChange"
    />
    <keyWarning
      v-if="!showHomePage && showKeyWarning"
      :bulletins-data="bulletinsData"
      :jq-detail-data="jqDetailData"
      :jq-process-data="jqProcessData"
      :message-platform-pie-chart-data="messagePlatformPieChartData"
      :sensitive-trend-data="sensitiveTrendData"
      :sensitivity-analysis-data="sensitivityAnalysisData"
      :zt-detail-data="ztDetailData"
      @handleBack="handleBack"
      @senInfoTimeClick="senInfoTimeClick"
    />
    <SensitiveInstance
        :jjdbh="jjdbh"
        :point-instance-data="pointInstanceData"
        :district-list="districtList"
        v-if="!showHomePage && showWarningInstance"
        @handleBack="handleBack"
    />
    <DetailDialog
      :current-view="currentView"
      :visible.sync="dialogVisible"
      :attention-color="attentionColor"
      :police-categorie="policeCategorie"
      @update:visible="handleDialogVisibleChange"
      @switchView="handleSwitchView"
    >
      <template #left>
        <!--        <component :is="currentComponent" v-if="currentComponent"></component>-->
      </template>
      <template #center>
        <!-- 中间固定内容 -->
      </template>
      <template #right>
        <!-- 右侧固定内容 -->
      </template>
    </DetailDialog>
    <!--    <chat-button v-if="showHomePage" />-->
  </div>
</template>
<script>
import commonHeader from "@/components/commonHeader.vue"; // 头部组件
import dailySituation from "@/components/common/dailySituation.vue"; // 左一当日情况
import publicOpinionCassification from "@/components/common/publicOpinionCassification.vue"; // 左二舆情分级
import publicOpinionCategory from "@/components/common/publicOpinionCategory.vue"; // 左三舆情类别
import smartPoliceManagement from "@/components/common/smartPoliceManagement.vue"; // 中间智警管理
import VideoPreview from "@/components/positiveRublicize/home/<USER>"; //中间视频预览
import mapPopup from "@/components/mapPopup.vue"; // 中间地图弹窗
import switchInterface from "@/components/common/switchInterface.vue"; //切换涉舆/正宣界面按钮
import point from "@/components/relatedPublic/home/<USER>"; // 右一重点警情
import keyEventsTable from "@/components/relatedPublic/home/<USER>"; // 右一重点事件
import wordCloud from "@/components/relatedPublic/home/<USER>";
import pointAccount from "@/components/relatedPublic/pointAccount/index.vue";
import gdMap from "@/components/map.vue";
import keyWarning from "@/components/relatedPublic/keyWarning/index.vue";
import sentinelSituation from "@/components/relatedPublic/home/<USER>"; // 哨兵情况
import SensitiveInstance from "@/components/relatedPublic/sensitiveInstance/index.vue";
import {
  dailyInstanceList,
  dailySubjectList,
  emphasizeActList,
  emphasizeActMsg,
  emphasizeUserList,
  getBulletins,
  getDealList,
  getMessagePlatformPieChart,
  getRelationIndexList,
  getScreenData,
  getSensitiveTrend,
  getSensitivityAnalysis,
  getSentryRank,
  getInstanceInfo,
  getSentryReports,
  getInstructionList,
} from "@/api/screen";
import CenterMap from "@/components/centerMap.vue";
import DetailDialog from "@/components/common/detailDialog.vue";
import Instruction from "@/components/relatedPublic/home/<USER>";
import ChatButton from "@/components/chatButton/index.vue";
import responsiveMixin from "@/mixins/responsive.js";
// import { EventBus } from '@/eventBus'
export default {
  mixins: [responsiveMixin],
  components: {
    SensitiveInstance,
    ChatButton,
    Instruction,
    CenterMap,
    commonHeader, // 头部组件
    dailySituation, // 左一当日情况
    publicOpinionCassification, // 左二舆情分级
    publicOpinionCategory, // 左三舆情类别
    smartPoliceManagement, // 左四智警管理
    VideoPreview,
    switchInterface,
    mapPopup, // 中间地图弹窗
    point, // 首页重点警情组件
    keyEventsTable,
    wordCloud, // 词云
    pointAccount, // 重点账号详情
    gdMap,
    keyWarning, // 涉舆-重点警情
    sentinelSituation, // 涉舆-哨兵情况
    DetailDialog,
  },
  data() {
    return {
      jjdbh: null,
      attentionColor: null,
      policeCategorie: null,
      instructionsData: null, // 批示流转数据
      rightTopIndex: 1,
      dialogVisible: false,
      currentView: "", // 新增当前视图控制
      item: null,
      childMounted: false,
      dailyData: null,
      opinionLevelData: null,
      opinionCategoryData: null,
      smartPoliceData: null,
      pointInstanceData: null,
      sentinelSituationData: null,
      wordCloudData: null,
      emphasizeActCurrentPage: 1,
      emphasizeActTotalPages: 1,
      emphasizeActPageSize: 15,
      emphasizeActTimer: null,
      emphasizeUserListData: null,
      platformOptions: null,
      emphasizeActMsgData: null,
      pointAccountTotal: null,
      pointAccountPagination: {},
      jqDetailData: null,
      ztDetailData: null,
      jqProcessData: null,
      sensitiveTrendData: null,
      messagePlatformPieChartData: null,
      sensitivityAnalysisData: null,
      bulletinsData: null,
      value: 0.7,
      showHomePage: true,
      showPointAccount: false,
      showKeyWarning: false,
      showWarningInstance: false,
      pageIndex: 1,
      keyEventsTableData1: [
        {
          id: 1,
          category: "求助",
          source: "批示",
          name: "涉警求助",
          time: "11-11 11:11:11",
        },
      ],
      keyEventsTableData2: [
        {
          id: 1,
          category: "求助",
          source: "上报",
          name: "涉警求助",
          time: "11-11 11:11:11",
        },
      ],
      districtList: [
        {
          district: "",
          name: "全部",
        },
      ],
      deptInfoList: [],
      showMapPop: true,
      sentinelCode: "",
      sentinelPageNum: null,
      sentinelSearchText: "",
    };
  },
  created() {
    this.getRelationIndexList();
    this.districtList = JSON.parse(localStorage.getItem("districtList"));
    // console.log(this.districtList)
  },
  mounted() {
    this.getDailyInstanceList();
    this.getDailySubjectList();
    this.getSentryRankList();
    this.getSentryReportsList();
    this.startEmphasizeActListCycle(); // 修改为启动循环调用
    // this.getEmphasizeUserList()
    // this.getEmphasizeActMsg()
    this.getLeftData();
    this.startTimer(); // 启动定时器
  },
  beforeDestroy() {
    this.clearTimer(); // 清除定时器
    this.clearEmphasizeActTimer(); // 清除重点账号循环定时器
  },
  methods: {
    handleRowClick2(jjdbh) {
      this.jjdbh = jjdbh;
      this.handleSensitiveInstanceClick();
    },
    handleSensitiveInstanceClick() {
      this.showHomePage = false;
      this.showPointAccount = false;
      this.showKeyWarning = false;
      this.showWarningInstance = true;
    },
    // 处理弹窗显示状态变化
    handleDialogVisibleChange(visible) {
      this.dialogVisible = visible;
      if (!visible) {
        // 弹窗关闭时重置相关状态
        this.currentView = "";
        this.attentionColor = null;
        this.policeCategorie = null;
      }
    },
    // 处理弹窗内视图切换
    handleSwitchView({ currentView, attentionColor, policeCategorie }) {
      console.log("切换视图:", { currentView, attentionColor, policeCategorie });
      this.currentView = currentView;
      this.attentionColor = attentionColor;
      this.policeCategorie = policeCategorie;
    },
    // 处理饼图分类点击事件
    handleCategoryClick(categoryName) {
      // 打开DetailDialog并传递参数
      this.dialogVisible = true;
      console.log(this.dialogVisible);
      this.currentView = "proportion";
      this.policeCategorie = categoryName;
    },
    // 处理
    handleBarClick(colorLevel) {
      this.dialogVisible = true;
      this.currentView = "classification";
      this.attentionColor = colorLevel; // 保存点击的颜色级别
    },
    // 获取批示流转列表
    getInstructionsList() {
      const data = {
        args: {
          startTime: "",
          endTime: "",
          instructionsContent: "",
          instructionsName: "",
          periodical: "",
        },
        pageable: {
          pageNumber: 1,
          pageSize: 100,
        },
      };
      getInstructionList(data)
        .then((res) => {
          console.log(res);
          this.instructionsData = res.data;
        })
        .catch((err) => {
          console.log(err);
        });
    },
    // 右侧顶部tab切换事件
    handleRightTopClick(index) {
      this.rightTopIndex = index;
      if (index === 3) {
        this.getInstructionsList();
      }
    },
    handleDailySituationClick(e) {
      // 获取点击的元素
      const target = e.target;

      // 向上查找最近的container-content-item元素
      const contentItem = target.closest(".container-content-item");
      if (!contentItem) return;

      // 获取所有container-content-item元素
      const items = document.querySelectorAll(".container-content-item");
      // 将NodeList转为数组并获取点击元素的索引
      const index = Array.from(items).indexOf(contentItem);

      this.dialogVisible = true;
      // 根据索引判断点击的是左侧还是右侧
      this.currentView = index === 0 ? "hangzhou" : "police";
    },
    handleComponentClick(componentName) {
      console.log("componentName", componentName);
      this.dialogVisible = true;

      // 根据组件名称设置要显示的内容
      switch (componentName) {
        case "publicOpinionCassification":
          this.currentView = "classification";
          break;
        case "publicOpinionCategory":
          this.currentView = "proportion";
          break;
      }
    },
    handleCurrentPage(toCurrentPage) {
      console.log("toCurrentPage", toCurrentPage);
      this.sentinelPageNum = toCurrentPage;
      this.getSentryReportsList();
    },
    // 重点账号词云点击事件
    handleDataInfo(item) {
      console.log(item);
      this.item = item;
      this.getEmphasizeActMsg(item);
      this.getEmphasizeUserList(item);
      this.handlePointAccountClick();
    },
    // 获取地区代码
    getRelationIndexList() {
      getRelationIndexList()
        .then((res) => {
          // console.log(res)
          const data = res.data;
          localStorage.setItem("districtList", JSON.stringify(data));
        })
        .catch((err) => {
          console.log(err);
        });
    },
    // 获取左侧数据
    getLeftData() {
      getScreenData()
        .then((res) => {
          const data = res.data;
          this.dailyData = data.opinionCountCondition;
          this.opinionLevelData = data.opinionLevel;
          this.opinionCategoryData = data.opinionCategory;
          this.smartPoliceData = data.policeInformationData;
          this.childMounted = true;
          // console.log(res)
        })
        .catch((err) => {
          console.log(err);
        });
    },
    startTimer() {
      this.timer = setInterval(() => {
        this.getLeftData(); // 每隔五分钟调用一次 getLeftData
      }, 5 * 60 * 1000); // 5分钟 = 5 * 60 * 1000 毫秒
    },
    clearTimer() {
      if (this.timer) {
        clearInterval(this.timer); // 清除定时器
        this.timer = null;
      }
    },
    // 获取重点警情列表
    getDailyInstanceList() {
      const data = {
        args: {
          // content: this.from.secarchText,
          startTime: "",
          endTime: "",
          // possession: this.from.district,
          // status: this.from.jqztValue,
          // status: this.from.jqztValue,
          // type: this.from.jqlxStr,
          displayType: 0,
          isAll: "",
        },
        pageable: {
          pageNumber: 1,
          pageSize: 10,
          // totalNum: 1,
          // totalPage: 1
        },
      };
      dailyInstanceList(data)
        .then((res) => {
          this.pointInstanceData = res.data;
          console.log(this.pointInstanceData);
        })
        .catch((err) => {
          console.log(err);
        });
    },
    // 获取每日主题事件
    getDailySubjectList() {
      const fetchData = (displayType) => {
        const data = {
          args: {
            fileEndTime: "",
            fileStartTime: "",
            isAll: "",
            displayType,
          },
          pageable: {
            pageNumber: 1,
            pageSize: 10,
          },
        };

        dailySubjectList(data)
          .then((res) => {
            // console.log('Response from subjectList:', res)
            if (displayType === 1) {
              this.keyEventsTableData1 = res.data;
              // console.log(this.keyEventsTableData1)
            } else if (displayType === 2) {
              this.keyEventsTableData2 = res.data;
              // console.log(this.keyEventsTableData2)
            }
          })
          .catch((err) => {
            console.log(err);
          });
      };

      // 调用 fetchData 分别处理 displayType 为 1 和 2 的情况
      fetchData(1);
      fetchData(2);
    },
    // 获取哨兵排名
    getSentryRankList() {
      getSentryRank()
        .then((res) => {
          console.log(res);
          // this.sentinelSituationData = res.data
          // 提取deptName和code存放到deptInfoList中
          this.deptInfoList = res.data.map((item) => ({
            deptName: item.deptName,
            code: item.code,
          }));
          console.log(this.deptInfoList);
        })
        .catch((err) => {
          console.log(err);
        });
    },
    // 获取哨兵报送数据
    getSentryReportsList() {
      const data = {
        code: this.sentinelCode || "",
        title: this.sentinelSearchText || "",
        pageNum: this.sentinelPageNum,
        pageSize: 12,
      };
      getSentryReports(data)
        .then((res) => {
          console.log(res);
          this.sentinelSituationData = res;
        })
        .catch((err) => {
          console.log(err);
        });
    },
    handleSearchContent(searchContent) {
      console.log(searchContent);
      this.sentinelCode = searchContent.sentinelCode;
      this.sentinelSearchText = searchContent.sentinelSearchText;
      this.getSentryReportsList();
    },
    // 获取重点账号列表（修改为支持分页）
    getEmphasizeActList() {
      const data = {
        args: {
          startTime: "",
          endTime: "",
        },
        pageable: {
          pageNumber: this.emphasizeActCurrentPage,
          pageSize: this.emphasizeActPageSize,
        },
      };
      emphasizeActList(data)
        .then((res) => {
          this.wordCloudData = res.data;

          if (res.pageable.totalPage) {
            this.emphasizeActTotalPages = res.pageable.totalPage;
          }

          console.log(
            `获取重点账号列表 - 当前页: ${this.emphasizeActCurrentPage}/${this.emphasizeActTotalPages}`
          );
        })
        .catch((err) => {
          console.log(err);
        });
    },
    // 启动重点账号列表循环调用
    startEmphasizeActListCycle() {
      // 先调用一次获取第一页数据和总页数
      this.getEmphasizeActList();

      // 设置定时器，每10秒切换到下一页
      this.emphasizeActTimer = setInterval(() => {
        // 页码加1，如果超过总页数则重置为1
        this.emphasizeActCurrentPage =
          this.emphasizeActCurrentPage >= this.emphasizeActTotalPages
            ? 1
            : this.emphasizeActCurrentPage + 1;

        // 调用获取数据方法
        this.getEmphasizeActList();
      }, 10000); // 10秒切换一次
    },
    // 清除重点账号循环定时器
    clearEmphasizeActTimer() {
      if (this.emphasizeActTimer) {
        clearInterval(this.emphasizeActTimer);
        this.emphasizeActTimer = null;
      }
    },
    // 获取重点关注人员下账号列表
    getEmphasizeUserList(item) {
      const data = {
        args: {
          name: item.name,
        },
      };
      emphasizeUserList(data)
        .then((res) => {
          this.emphasizeUserListData = res.data;
          console.log(res);
          this.platformOptions = res.data.map((item) => {
            console.log(item);
            return { value: item.id, label: item.nikeName };
          });
          // console.log(this.platformOptions)
        })
        .catch((err) => {
          console.log(err);
        });

      // this.from.accountValue = this.from.accountList.find(item => item.value === this.$route.params.id).value
      // this.accountId = this.from.accountValue
      // this.getEmphasizeActMsg()
    },
    // 获取重点账号发布信息
    getEmphasizeActMsg(item, pageNumber = 1, pageSize = 15) {
      const data = {
        args: {
          startTime: "",
          endTime: "",
          accountId: item.id || item,
        },
        pageable: {
          pageNumber,
          pageSize,
        },
      };
      emphasizeActMsg(data)
        .then((res) => {
          console.log(res);
          this.emphasizeActMsgData = res.data;
          this.pointAccountPagination = res.pageable;
          console.log(this.pointAccountPagination);
          this.showPointAccount = true; // 显示pointAccount组件
          this.showHomePage = true; // 隐藏主页
        })
        .catch((err) => {
          console.log(err);
        });
    },
    // 更新分页数据
    updatePagination({ pageNumber, pageSize }) {
      this.getEmphasizeActMsg(this.item, pageNumber, pageSize);
    },
    // 更新账号列表
    handlePlatformChange(val) {
      this.getEmphasizeActMsg(val, 1, 15);
    },
    // 根据警情id获取警情详情
    getJqDetails(subjectId) {
      const jjdbh = subjectId;
      getInstanceInfo(jjdbh)
        .then((res) => {
          this.jqDetailData = res.data;
        })
        .catch((err) => {
          console.log(err);
        });
    },
    // 根据专题id获取处警流程
    getJqProcessData(subjectId, instanceId) {
      const data = {
        args: instanceId ? { instanceId } : { subjectId },
      };
      getDealList(data)
        .then((res) => {
          console.log(res);
          this.jqProcessData = res.data;
        })
        .catch((err) => {
          console.log(err);
        });
    },
    // 根据专题id获取敏感信息走势
    getSensitiveTrendData(subjectId, type) {
      getSensitiveTrend({ subjectId, type })
        .then((res) => {
          console.log(res);
          this.sensitiveTrendData = res.data;
        })
        .catch((err) => {
          console.log(err);
        });
    },
    // 根据专题id获取信息来源平台饼形图
    getMessagePlatformPieChartData(subjectId) {
      getMessagePlatformPieChart({ subjectId })
        .then((res) => {
          console.log(res);
          this.messagePlatformPieChartData = res.data;
        })
        .catch((err) => {
          console.log(err);
        });
    },
    // 根据专题id获取敏感度饼形图
    getSensitivityAnalysisData(subjectId) {
      getSensitivityAnalysis({ subjectId })
        .then((res) => {
          console.log(res);
          this.sensitivityAnalysisData = res.data;
        })
        .catch((err) => {
          console.log(err);
        });
    },
    // 每日事件表格行点击事件
    handleRowClick(subjectId, instanceId, extId, data) {
      this.ztDetailData = data;
      console.log("handleRowClick");
      this.getJqDetails(subjectId);
      this.getJqProcessData(subjectId, instanceId);
      this.getSensitiveTrendData(subjectId, 0);
      this.getMessagePlatformPieChartData(subjectId);
      this.getSensitivityAnalysisData(subjectId);
      this.getBulletinsData(extId);
      this.handleKeyWarningClick();
    },
    // 根据专题id获取每日快报
    getBulletinsData(extId) {
      getBulletins({ subjectId: extId })
        .then((res) => {
          console.log(res);
          this.bulletinsData = res.data;
        })
        .catch((err) => {
          console.log(err);
        });
    },
    handlePointAccountClick() {
      // 点击查看重点账号详情
      this.showHomePage = true;
      this.showPointAccount = true;
      this.showKeyWarning = false;
    },
    handleBack(val) {
      this.showHomePage = true;
      this.showPointAccount = false;
      this.showKeyWarning = false;
      this.showWarningInstance = false;
    },
    senInfoTimeClick(str) {
      let subjectId = this.ztDetailData.subjectId;
      this.getSensitiveTrendData(subjectId, str);
    },
    handleKeyWarningClick() {
      // 点击查看正宣警情
      this.showHomePage = false;
      this.showPointAccount = false;
      this.showKeyWarning = true;
    },
    handleBUttonClick(type) {
      this.$nextTick(() => {
        this.pageIndex = type;
      });
    },
    showMapPopup() {
      this.showMapPop = true;

      // 调用子组件中的方法
      this.$refs.mapPopup.getLatestMediaMessage();
      this.$refs.mapPopup.getLatestDailyInstanceList();
    },
  },
};
</script>
<style lang="scss" scoped>
@font-face {
  font-family: "ShangShouJiangHuShuFaTi-2";
  src: url("../../assets/fonts/ShangShouJiangHuShuFaTi-2.ttf");
}

.container-home {
  position: relative;
  width: 100%;
  height: 100%;
  background: url("../../assets/images/bjt.png") no-repeat;
  background-size: 100% 100%;

  .map {
    width: 100%;
    height: 100%;
    //background: url("../../assets/images/bg-map.jpg") no-repeat;
  }

  .common-header {
    z-index: 9;
    position: absolute;
    width: 100%;
    height: 85px;

    &::before {
      //content: "";
      width: 24.5vw;
      height: 11px;
      position: absolute;
      left: 50%;
      transform: translateX(-50%);
      bottom: 59px;
      z-index: 9;
      //border: 11px solid transparent; /* 设置边框为透明 */
      background: linear-gradient(45deg, #e2f2f9, #18a3e5, #e2f2f9) 0% 0% / 200%
        100%; /* 设置背景渐变 */
      clip-path: inset(0px round 10px);
      animation: flowBorder 2s infinite linear;
    }

    @keyframes flowBorder {
      0% {
        background-position: 200% 0%; /* 起始位置 */
      }
      100% {
        background-position: 0% 0%; /* 结束位置 */
      }
    }
  }

  .map-pop {
    z-index: 1000;
    position: absolute;
    right: 775px;
    top: 46px;
  }

  .map-pop1 {
    z-index: 1000;
    position: absolute;
    right: 800px;
    top: 74px;
  }

  .container-left {
    position: absolute;
    top: 0;
    left: 0;
    // padding-top: 118px;
    // padding-left: 26px;
    width: 757px;
    height: 1040px;
    //background-image: linear-gradient(
    //  to right,
    //  rgba($color: #102054, $alpha: 1),
    //  rgba($color: #102054, $alpha: 0.96) 60%,
    //  rgba($color: #102054, $alpha: 0.8)
    //);
    //box-shadow: 60px 0 60px rgba($color: #102054, $alpha: 0.8);
  }
  .smartpolice {
    position: absolute;
    left: 757px;
    bottom: 0;
  }

  .videopreview {
    position: absolute;
    bottom: 20px;
    left: 1529px;
    width: 737px;
    height: 314px;
  }
  .switchinterface {
    position: absolute;
    bottom: 367px;
    left: 2061px;
    width: 204px;
    height: 44px;
  }
  .container-right {
    position: absolute;
    top: 0;
    right: 0;
    padding-top: 48px;
    padding-right: 20px;
    height: 1040px;
    // background-image: linear-gradient(
    //   to left,
    //   rgba($color: #102054, $alpha: 1),
    //   rgba($color: #102054, $alpha: 0.96) 60%,
    //   rgba($color: #102054, $alpha: 0.8)
    // );
    //box-shadow: -60px 0 60px rgba($color: #102054, $alpha: 0.8);
  }
}

// 舆情类别
.category_piechart {
  display: flex;
  align-items: center;
  margin-top: 16px;
}

// 智警管理
.manage {
  height: 185px;
  width: 100%;
  display: flex;
  justify-content: space-between;
  margin-bottom: 0px;
}

.manage_1 {
  height: 100%;
  width: 100%;
}

.manage_2 {
  height: 100%;
  width: 100%;
  margin-right: 47px;
}

:root {
  --b-clr1: rgb(140, 206, 247);
  --b-clr2: rgb(124, 146, 255);
}

@keyframes clippath {
  0%,
  100% {
    clip-path: inset(0 0 98% 0);
  }

  75% {
    clip-path: inset(0 98% 0 0);
  }
  50% {
    clip-path: inset(98% 0 0 0);
  }
  25% {
    clip-path: inset(0 0 0 98%);
  }
}

// 舆情类别
.right_1 {
  width: 737px;
  height: 314px;
  background: rgba(0, 8, 20, 0.7);
  backdrop-filter: blur(50px);
  position: relative;
  z-index: 1000;
  margin-bottom: 10px;
  color: #fff;
  //border-radius: 10px;
  //background: gold;
  transition: all 0.3s;
  border-radius: 10px;
  padding: 20px 24px 0px;

  &:hover {
    filter: contrast(1.1);
  }

  &:active {
    filter: contrast(0.9);
  }

  // &::before,
  // &::after {
  //   content: "";
  //   position: absolute;
  //   top: -2px;
  //   left: -2px;
  //   right: -2px;
  //   bottom: -2px;
  //   border: 3px solid;
  //   border-image: linear-gradient(to right, #85dcf1, #0574e9) 1;
  //   transition: all 0.5s;
  //   animation: clippath 5s infinite linear;
  //   border-radius: 10px;
  // }

  //&::after {
  //  animation: clippath 2.5s infinite -1.25s linear;
  //}
}

// 重点警情
.scroll_table {
  width: 690px;
}

.right_2 {
  //margin-top: 10px;
  width: 737px;
  height: 314px;
  background: rgba(0, 8, 20, 0.7);
  backdrop-filter: blur(50px);
  position: relative;
  margin: auto;
  color: #fff;
  //border-radius: 10px;
  //background: gold;
  transition: all 0.3s;
  border-radius: 10px;
  padding: 20px 24px 0px;
  &:hover {
    filter: contrast(1.1);
  }

  &:active {
    filter: contrast(0.9);
  }

  // &::before,
  // &::after {
  //   content: "";
  //   position: absolute;
  //   top: -2px;
  //   left: -2px;
  //   right: -2px;
  //   bottom: -2px;
  //   border: 3px solid;
  //   border-image: linear-gradient(to right, #85dcf1, #0574e9) 1;
  //   transition: all 0.5s;
  //   animation: clippath 7.5s infinite linear;
  //   border-radius: 10px;
  // }
}

.today_tit {
  display: flex;
  align-items: center;

  .geomancy {
    cursor: pointer;
    text-align: center;
    width: 78px;
    height: 24px;
    background: url("../../assets/images/qjtsk_x.png") no-repeat;
    background-size: 100% 100%;
    font-size: 11px;
    color: #c5e8f6;
    line-height: 24px;
  }
}

.declare {
  cursor: pointer;
  text-align: center;
  width: 78px;
  height: 24px;
  line-height: 24px;
  background: url("../../assets/images/qtspk.png") no-repeat;
  background-size: 100% 100%;
  font-size: 11px;
  color: #c5e8f6;
}

.right_3 {
  width: 737px;
  height: 314px;
  background: rgba(0, 8, 20, 0.7);
  backdrop-filter: blur(50px);
  margin-top: 10px;
  position: relative;
  font-family: PingFangSC, PingFang SC;
  font-weight: 600;
  font-size: 22px;
  color: #ffffff;
  line-height: 31px;
  text-align: left;
  font-style: normal;
  border-radius: 10px;
  transition: all 0.3s;
  padding: 20px 24px 0px;
  &:hover {
    filter: contrast(1.1);
  }

  &:active {
    filter: contrast(0.9);
  }
}

// 中间地图
.container-center {
  position: relative;
  width: 50%;
  margin-top: -13px;
  // background: url('@/assets/images/map-popup/map.png') no-repeat;
  // background-size: 100% 100%;
}

.popup-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  .today_tit {
    font-family: PingFangSC, PingFang SC;
    font-weight: 600;
    font-size: 22px;
    color: #ffffff;
    line-height: 31px;
    text-align: left;
    font-style: normal;
  }
  .header-button-group {
    display: flex;
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    font-size: 14px;
    background-color: #000;
    border-radius: 17px;
    color: #fff;
    .header-button-item {
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      text-align: center;
      font-size: 14px;
      line-height: 21px;
      width: 82px;
      height: 32px;
      border-radius: 17px;
      color: rgba(255, 255, 255, 0.6);
    }
    .header-button-active {
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: 600 !important;
      cursor: pointer;
      background-color: rgba(255, 255, 255, 0.2);
      color: #ffffff;
    }
  }

  // .header-button-item-1 {
  //   width: 80px;
  //   background: url("../../assets/images/map-popup/bg-1-inactive.png");
  //   background-size: 100% 100%;
  // }

  // .header-button-item-2 {
  //   width: 90px;
  //   background: url("../../assets/images/map-popup/bg-2-inactive.png");
  //   background-size: 100% 100%;
  // }

  // .header-button-active-1,
  // .header-button-active-2 {
  //   span {
  //     background: linear-gradient(0deg, #76c0ff 0%, #ffffff 100%);
  //     -webkit-background-clip: text;
  //     -webkit-text-fill-color: transparent;
  //   }
  // }

  // .header-button-active-1 {
  //   background: url("../../assets/images/map-popup/bg-1-active.png") no-repeat;
  //   background-size: 100% 100%;
  // }

  // .header-button-active-2 {
  //   width: 80px;
  //   background: url("../../assets/images/map-popup/bg-2-active.png") no-repeat;
  //   background-size: 100% 100%;
  // }
}
</style>
